#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLabel>
#include <QSlider>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFileDialog>
#include <QMessageBox>
#include "audioplayer.h"

QT_BEGIN_NAMESPACE
class QLabel;
class QSlider;
class QPushButton;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void openFile();
    void playPause();
    void stop();
    void setPosition(int position);
    void setVolume(int volume);
    void updatePosition(qint64 position);
    void updateDuration(qint64 duration);
    void updatePlaybackState();

private:
    void setupUI();
    QString formatTime(qint64 timeInMs);

    AudioPlayer *m_audioPlayer;
    
    // UI Components
    QPushButton *m_openButton;
    QPushButton *m_playPauseButton;
    QPushButton *m_stopButton;
    QSlider *m_positionSlider;
    QSlider *m_volumeSlider;
    QLabel *m_currentTimeLabel;
    QLabel *m_totalTimeLabel;
    QLabel *m_fileNameLabel;
    QLabel *m_volumeLabel;
    
    qint64 m_duration;
};

#endif // MAINWINDOW_H
