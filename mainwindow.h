#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QLabel>
#include <QSlider>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFileDialog>
#include <QMessageBox>
#include <QListWidget>
#include <QSplitter>
#include <QTabWidget>
#include <QMenuBar>
#include <QStatusBar>
#include <QProgressBar>
#include <QTimer>
#include <QSystemTrayIcon>
#include <QAction>
#include <QShortcut>
#include <QSpinBox>
#include <QCheckBox>
#include "audioplayer.h"
#include "playlist.h"
#include "equalizer.h"

QT_BEGIN_NAMESPACE
class QLabel;
class QSlider;
class QPushButton;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    // File operations
    void openFile();
    void openFiles();
    void openFolder();
    void savePlaylist();
    void loadPlaylist();

    // Playback control
    void playPause();
    void stop();
    void nextTrack();
    void previousTrack();
    void setPosition(int position);
    void setVolume(int volume);
    void setSpeed(double speed);

    // Playlist operations
    void shuffleToggle();
    void repeatToggle();
    void removeFromPlaylist();
    void clearPlaylist();
    void playlistItemDoubleClicked(QListWidgetItem *item);
    void onPlaylistChanged();
    void onCurrentTrackChanged(int index);

    // Audio updates
    void updatePosition(qint64 position);
    void updateDuration(qint64 duration);
    void updatePlaybackState();

    // UI and settings
    void showEqualizer();
    void showSettings();
    void togglePlaylistView();
    void onSystemTrayActivated(QSystemTrayIcon::ActivationReason reason);

    // Keyboard shortcuts
    void setupShortcuts();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupSystemTray();
    void loadStyleSheet();
    QString formatTime(qint64 timeInMs);
    void updatePlaylistDisplay();
    void updateWindowTitle();

    // Core components
    AudioPlayer *m_audioPlayer;
    Playlist *m_playlist;
    Equalizer *m_equalizer;

    // Main UI Components
    QSplitter *m_mainSplitter;
    QTabWidget *m_rightPanel;

    // Control buttons
    QPushButton *m_openButton;
    QPushButton *m_playPauseButton;
    QPushButton *m_stopButton;
    QPushButton *m_nextButton;
    QPushButton *m_previousButton;
    QPushButton *m_shuffleButton;
    QPushButton *m_repeatButton;
    QPushButton *m_equalizerButton;
    QPushButton *m_playlistButton;

    // Sliders and controls
    QSlider *m_positionSlider;
    QSlider *m_volumeSlider;
    QSpinBox *m_speedSpinBox;

    // Labels
    QLabel *m_currentTimeLabel;
    QLabel *m_totalTimeLabel;
    QLabel *m_fileNameLabel;
    QLabel *m_artistLabel;
    QLabel *m_albumLabel;
    QLabel *m_volumeLabel;
    QLabel *m_speedLabel;

    // Playlist
    QListWidget *m_playlistWidget;

    // System integration
    QSystemTrayIcon *m_systemTray;
    QMenu *m_trayMenu;

    // Status bar
    QProgressBar *m_loadingProgress;
    QLabel *m_statusLabel;

    // State
    qint64 m_duration;
    bool m_playlistVisible;

    // Actions
    QAction *m_playAction;
    QAction *m_stopAction;
    QAction *m_nextAction;
    QAction *m_previousAction;
    QAction *m_shuffleAction;
    QAction *m_repeatAction;
};

#endif // MAINWINDOW_H
