QT += core widgets multimedia

CONFIG += c++17

TARGET = AudioPlayer
TEMPLATE = app

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    audioplayer.cpp

HEADERS += \
    mainwindow.h \
    audioplayer.h

FORMS += \
    mainwindow.ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
