QT += core widgets multimedia multimediawidgets

CONFIG += c++17

TARGET = AudioPlayer
TEMPLATE = app

SOURCES += \
    main.cpp \
    mainwindow.cpp \
    audioplayer.cpp \
    playlist.cpp \
    equalizer.cpp \
    settingsdialog.cpp

HEADERS += \
    mainwindow.h \
    audioplayer.h \
    playlist.h \
    equalizer.h \
    settingsdialog.h

FORMS += \
    mainwindow.ui \
    settingsdialog.ui

RESOURCES += \
    resources.qrc

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
