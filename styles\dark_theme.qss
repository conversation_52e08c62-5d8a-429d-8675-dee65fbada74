/* Modern Dark Theme for Audio Player */

QMainWindow {
    background-color: #1e1e1e;
    color: #ffffff;
    font-family: "Segoe UI", Arial, sans-serif;
}

QWidget {
    background-color: #1e1e1e;
    color: #ffffff;
    font-size: 10pt;
}

/* Buttons */
QPushButton {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 8px 16px;
    color: #ffffff;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #3d3d3d;
    border-color: #505050;
}

QPushButton:pressed {
    background-color: #1d1d1d;
    border-color: #303030;
}

QPushButton:disabled {
    background-color: #1a1a1a;
    color: #666666;
    border-color: #2a2a2a;
}

/* Control Buttons */
QPushButton#playPauseButton, QPushButton#stopButton, 
QPushButton#nextButton, QPushButton#previousButton {
    background-color: #0078d4;
    border: none;
    border-radius: 20px;
    min-width: 40px;
    min-height: 40px;
    font-size: 14px;
}

QPushButton#playPauseButton:hover, QPushButton#stopButton:hover,
QPushButton#nextButton:hover, QPushButton#previousButton:hover {
    background-color: #106ebe;
}

QPushButton#openButton {
    background-color: #107c10;
    border: none;
}

QPushButton#openButton:hover {
    background-color: #0e6e0e;
}

/* Sliders */
QSlider::groove:horizontal {
    border: none;
    height: 6px;
    background: #404040;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background: #0078d4;
    border: none;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -5px 0;
}

QSlider::handle:horizontal:hover {
    background: #106ebe;
}

QSlider::sub-page:horizontal {
    background: #0078d4;
    border-radius: 3px;
}

/* Volume Slider */
QSlider#volumeSlider::groove:horizontal {
    height: 4px;
    background: #404040;
    border-radius: 2px;
}

QSlider#volumeSlider::handle:horizontal {
    width: 12px;
    height: 12px;
    border-radius: 6px;
    margin: -4px 0;
}

/* Labels */
QLabel {
    color: #ffffff;
    background: transparent;
}

QLabel#fileNameLabel {
    font-size: 14pt;
    font-weight: 600;
    color: #ffffff;
    padding: 10px;
}

QLabel#currentTimeLabel, QLabel#totalTimeLabel {
    font-family: "Consolas", monospace;
    font-size: 9pt;
    color: #cccccc;
    min-width: 40px;
}

/* List Widget (for playlist) */
QListWidget {
    background-color: #252525;
    border: 1px solid #404040;
    border-radius: 4px;
    alternate-background-color: #2a2a2a;
    selection-background-color: #0078d4;
    outline: none;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #333333;
}

QListWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #3d3d3d;
}

/* Group Box */
QGroupBox {
    font-weight: 600;
    border: 1px solid #404040;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #ffffff;
}

/* Tab Widget */
QTabWidget::pane {
    border: 1px solid #404040;
    background-color: #1e1e1e;
}

QTabBar::tab {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #3d3d3d;
}

/* Scroll Bar */
QScrollBar:vertical {
    background: #2d2d2d;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background: #505050;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: #606060;
}

/* Menu Bar */
QMenuBar {
    background-color: #1e1e1e;
    color: #ffffff;
    border-bottom: 1px solid #404040;
}

QMenuBar::item {
    padding: 4px 8px;
    background: transparent;
}

QMenuBar::item:selected {
    background-color: #0078d4;
}

QMenu {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    color: #ffffff;
}

QMenu::item {
    padding: 6px 20px;
}

QMenu::item:selected {
    background-color: #0078d4;
}

/* Status Bar */
QStatusBar {
    background-color: #1e1e1e;
    border-top: 1px solid #404040;
    color: #cccccc;
}

/* Combo Box */
QComboBox {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #505050;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #ffffff;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    selection-background-color: #0078d4;
}

/* Spin Box */
QSpinBox, QDoubleSpinBox {
    background-color: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 4px;
    padding: 4px;
}

QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #505050;
}

/* Check Box */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #404040;
    border-radius: 3px;
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #0078d4;
    border-color: #0078d4;
}

QCheckBox::indicator:checked:hover {
    background-color: #106ebe;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #404040;
    border-radius: 3px;
    text-align: center;
    background-color: #2d2d2d;
}

QProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 2px;
}

/* Tool Tip */
QToolTip {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #404040;
    padding: 4px;
    border-radius: 4px;
}
