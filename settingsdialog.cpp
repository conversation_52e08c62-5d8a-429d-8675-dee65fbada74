#include "settingsdialog.h"
#include <QDialogButtonBox>
#include <QApplication>

SettingsDialog::SettingsDialog(QWidget *parent)
    : QDialog(parent)
    , m_settings(new QSettings(this))
{
    setupUI();
    loadSettings();
    
    connect(this, &QDialog::accepted, this, &SettingsDialog::onAccepted);
    connect(this, &QDialog::rejected, this, &SettingsDialog::onRejected);
}

void SettingsDialog::setupUI()
{
    setWindowTitle("Settings");
    setModal(true);
    resize(500, 400);
    
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    m_tabWidget = new QTabWidget(this);
    mainLayout->addWidget(m_tabWidget);
    
    setupGeneralTab();
    setupPlaybackTab();
    setupAppearanceTab();
    setupAdvancedTab();
    
    // Button box
    QDialogButtonBox *buttonBox = new QDialogButtonBox(
        QDialogButtonBox::Ok | QDialogButtonBox::Cancel | QDialogButtonBox::RestoreDefaults,
        this);
    
    connect(buttonBox, &QDialogButtonBox::accepted, this, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    connect(buttonBox->button(QDialogButtonBox::RestoreDefaults), 
            &QPushButton::clicked, this, &SettingsDialog::onRestoreDefaults);
    
    mainLayout->addWidget(buttonBox);
}

void SettingsDialog::setupGeneralTab()
{
    QWidget *generalTab = new QWidget(this);
    QVBoxLayout *layout = new QVBoxLayout(generalTab);
    
    // Startup group
    QGroupBox *startupGroup = new QGroupBox("Startup", this);
    QVBoxLayout *startupLayout = new QVBoxLayout(startupGroup);
    
    m_startMinimizedCheck = new QCheckBox("Start minimized", this);
    m_minimizeToTrayCheck = new QCheckBox("Minimize to system tray", this);
    
    startupLayout->addWidget(m_startMinimizedCheck);
    startupLayout->addWidget(m_minimizeToTrayCheck);
    
    layout->addWidget(startupGroup);
    
    // Playback group
    QGroupBox *playbackGroup = new QGroupBox("Playback", this);
    QVBoxLayout *playbackLayout = new QVBoxLayout(playbackGroup);
    
    m_autoPlayCheck = new QCheckBox("Auto-play when files are added", this);
    m_rememberPositionCheck = new QCheckBox("Remember playback position", this);
    
    playbackLayout->addWidget(m_autoPlayCheck);
    playbackLayout->addWidget(m_rememberPositionCheck);
    
    layout->addWidget(playbackGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(generalTab, "General");
}

void SettingsDialog::setupPlaybackTab()
{
    QWidget *playbackTab = new QWidget(this);
    QFormLayout *layout = new QFormLayout(playbackTab);
    
    // Crossfade
    m_crossfadeSpin = new QSpinBox(this);
    m_crossfadeSpin->setRange(0, 10000);
    m_crossfadeSpin->setSuffix(" ms");
    m_crossfadeSpin->setValue(0);
    layout->addRow("Crossfade duration:", m_crossfadeSpin);
    
    // Gapless playback
    m_gaplessPlaybackCheck = new QCheckBox("Enable gapless playback", this);
    layout->addRow(m_gaplessPlaybackCheck);
    
    // Output device
    m_outputDeviceCombo = new QComboBox(this);
    m_outputDeviceCombo->addItem("Default");
    layout->addRow("Output device:", m_outputDeviceCombo);
    
    // Buffer size
    m_bufferSizeSlider = new QSlider(Qt::Horizontal, this);
    m_bufferSizeSlider->setRange(1, 10);
    m_bufferSizeSlider->setValue(5);
    QLabel *bufferLabel = new QLabel("Buffer size:", this);
    layout->addRow(bufferLabel, m_bufferSizeSlider);
    
    m_tabWidget->addTab(playbackTab, "Playback");
}

void SettingsDialog::setupAppearanceTab()
{
    QWidget *appearanceTab = new QWidget(this);
    QFormLayout *layout = new QFormLayout(appearanceTab);
    
    // Theme
    m_themeCombo = new QComboBox(this);
    m_themeCombo->addItems({"Dark", "Light", "System"});
    connect(m_themeCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &SettingsDialog::onThemeChanged);
    layout->addRow("Theme:", m_themeCombo);
    
    // Font
    m_fontButton = new QPushButton("Choose Font...", this);
    layout->addRow("Font:", m_fontButton);
    
    // Color
    m_colorButton = new QPushButton("Accent Color...", this);
    layout->addRow("Accent color:", m_colorButton);
    
    // Visualization
    m_showVisualizationCheck = new QCheckBox("Show audio visualization", this);
    layout->addRow(m_showVisualizationCheck);
    
    m_tabWidget->addTab(appearanceTab, "Appearance");
}

void SettingsDialog::setupAdvancedTab()
{
    QWidget *advancedTab = new QWidget(this);
    QFormLayout *layout = new QFormLayout(advancedTab);
    
    // Logging
    m_enableLoggingCheck = new QCheckBox("Enable debug logging", this);
    layout->addRow(m_enableLoggingCheck);
    
    // Recent files
    m_maxRecentFilesSpin = new QSpinBox(this);
    m_maxRecentFilesSpin->setRange(0, 50);
    m_maxRecentFilesSpin->setValue(10);
    layout->addRow("Max recent files:", m_maxRecentFilesSpin);
    
    // Updates
    m_checkUpdatesCheck = new QCheckBox("Check for updates on startup", this);
    layout->addRow(m_checkUpdatesCheck);
    
    m_tabWidget->addTab(advancedTab, "Advanced");
}

void SettingsDialog::loadSettings()
{
    m_startMinimizedCheck->setChecked(m_settings->value("general/startMinimized", false).toBool());
    m_minimizeToTrayCheck->setChecked(m_settings->value("general/minimizeToTray", true).toBool());
    m_autoPlayCheck->setChecked(m_settings->value("general/autoPlay", false).toBool());
    m_rememberPositionCheck->setChecked(m_settings->value("general/rememberPosition", true).toBool());
    
    m_crossfadeSpin->setValue(m_settings->value("playback/crossfade", 0).toInt());
    m_gaplessPlaybackCheck->setChecked(m_settings->value("playback/gapless", true).toBool());
    
    m_themeCombo->setCurrentText(m_settings->value("appearance/theme", "Dark").toString());
    m_showVisualizationCheck->setChecked(m_settings->value("appearance/visualization", false).toBool());
    
    m_enableLoggingCheck->setChecked(m_settings->value("advanced/logging", false).toBool());
    m_maxRecentFilesSpin->setValue(m_settings->value("advanced/maxRecentFiles", 10).toInt());
    m_checkUpdatesCheck->setChecked(m_settings->value("advanced/checkUpdates", true).toBool());
}

void SettingsDialog::saveSettings()
{
    m_settings->setValue("general/startMinimized", m_startMinimizedCheck->isChecked());
    m_settings->setValue("general/minimizeToTray", m_minimizeToTrayCheck->isChecked());
    m_settings->setValue("general/autoPlay", m_autoPlayCheck->isChecked());
    m_settings->setValue("general/rememberPosition", m_rememberPositionCheck->isChecked());
    
    m_settings->setValue("playback/crossfade", m_crossfadeSpin->value());
    m_settings->setValue("playback/gapless", m_gaplessPlaybackCheck->isChecked());
    
    m_settings->setValue("appearance/theme", m_themeCombo->currentText());
    m_settings->setValue("appearance/visualization", m_showVisualizationCheck->isChecked());
    
    m_settings->setValue("advanced/logging", m_enableLoggingCheck->isChecked());
    m_settings->setValue("advanced/maxRecentFiles", m_maxRecentFilesSpin->value());
    m_settings->setValue("advanced/checkUpdates", m_checkUpdatesCheck->isChecked());
}

void SettingsDialog::onAccepted()
{
    saveSettings();
    emit settingsChanged();
}

void SettingsDialog::onRejected()
{
    loadSettings(); // Revert changes
}

void SettingsDialog::onRestoreDefaults()
{
    m_startMinimizedCheck->setChecked(false);
    m_minimizeToTrayCheck->setChecked(true);
    m_autoPlayCheck->setChecked(false);
    m_rememberPositionCheck->setChecked(true);
    
    m_crossfadeSpin->setValue(0);
    m_gaplessPlaybackCheck->setChecked(true);
    
    m_themeCombo->setCurrentText("Dark");
    m_showVisualizationCheck->setChecked(false);
    
    m_enableLoggingCheck->setChecked(false);
    m_maxRecentFilesSpin->setValue(10);
    m_checkUpdatesCheck->setChecked(true);
}

void SettingsDialog::onThemeChanged()
{
    // Theme change would be handled here
}

// Getter methods
bool SettingsDialog::getMinimizeToTray() const { return m_minimizeToTrayCheck->isChecked(); }
bool SettingsDialog::getStartMinimized() const { return m_startMinimizedCheck->isChecked(); }
bool SettingsDialog::getAutoPlay() const { return m_autoPlayCheck->isChecked(); }
bool SettingsDialog::getRememberPosition() const { return m_rememberPositionCheck->isChecked(); }
int SettingsDialog::getCrossfadeDuration() const { return m_crossfadeSpin->value(); }
QString SettingsDialog::getTheme() const { return m_themeCombo->currentText(); }

// Setter methods
void SettingsDialog::setMinimizeToTray(bool enabled) { m_minimizeToTrayCheck->setChecked(enabled); }
void SettingsDialog::setStartMinimized(bool enabled) { m_startMinimizedCheck->setChecked(enabled); }
void SettingsDialog::setAutoPlay(bool enabled) { m_autoPlayCheck->setChecked(enabled); }
void SettingsDialog::setRememberPosition(bool enabled) { m_rememberPositionCheck->setChecked(enabled); }
void SettingsDialog::setCrossfadeDuration(int duration) { m_crossfadeSpin->setValue(duration); }
void SettingsDialog::setTheme(const QString &theme) { m_themeCombo->setCurrentText(theme); }
