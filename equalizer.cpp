#include "equalizer.h"
#include <QGridLayout>
#include <QApplication>

const double Equalizer::MIN_GAIN = -20.0;
const double Equalizer::MAX_GAIN = 20.0;

Equalizer::Equalizer(QWidget *parent)
    : QWidget(parent)
    , m_enabled(false)
{
    setupUI();
    setupPresets();
}

void Equalizer::setupUI()
{
    setWindowTitle("Equalizer");
    setFixedSize(600, 300);
    
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // Control section
    QHBoxLayout *controlLayout = new QHBoxLayout();
    
    m_enabledCheckBox = new QCheckBox("Enable Equalizer", this);
    m_presetComboBox = new QComboBox(this);
    m_resetButton = new QPushButton("Reset", this);
    
    controlLayout->addWidget(m_enabledCheckBox);
    controlLayout->addWidget(new QLabel("Preset:", this));
    controlLayout->addWidget(m_presetComboBox);
    controlLayout->addWidget(m_resetButton);
    controlLayout->addStretch();
    
    mainLayout->addLayout(controlLayout);
    
    // Equalizer bands
    m_equalizerGroup = new QGroupBox("Frequency Bands", this);
    QGridLayout *bandsLayout = new QGridLayout(m_equalizerGroup);
    
    // Initialize bands with standard frequencies
    QStringList frequencies = {"32Hz", "64Hz", "125Hz", "250Hz", "500Hz", 
                              "1kHz", "2kHz", "4kHz", "8kHz", "16kHz"};
    QList<double> freqValues = {32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000};
    
    for (int i = 0; i < BAND_COUNT; ++i) {
        m_bands.append(EqualizerBand(frequencies[i], freqValues[i]));
        
        // Frequency label
        QLabel *freqLabel = new QLabel(frequencies[i], this);
        freqLabel->setAlignment(Qt::AlignCenter);
        freqLabel->setStyleSheet("font-weight: bold; font-size: 9pt;");
        m_frequencyLabels.append(freqLabel);
        bandsLayout->addWidget(freqLabel, 0, i);
        
        // Gain slider
        QSlider *slider = new QSlider(Qt::Vertical, this);
        slider->setRange(-200, 200); // -20dB to +20dB (scaled by 10)
        slider->setValue(0);
        slider->setTickPosition(QSlider::TicksBothSides);
        slider->setTickInterval(50);
        slider->setMinimumHeight(150);
        slider->setProperty("bandIndex", i);
        
        connect(slider, QOverload<int>::of(&QSlider::valueChanged),
                this, &Equalizer::onBandSliderChanged);
        
        m_bandSliders.append(slider);
        bandsLayout->addWidget(slider, 1, i);
        
        // Gain label
        QLabel *gainLabel = new QLabel("0dB", this);
        gainLabel->setAlignment(Qt::AlignCenter);
        gainLabel->setStyleSheet("font-family: monospace; font-size: 8pt;");
        m_bandLabels.append(gainLabel);
        bandsLayout->addWidget(gainLabel, 2, i);
    }
    
    mainLayout->addWidget(m_equalizerGroup);
    
    // Connect signals
    connect(m_enabledCheckBox, &QCheckBox::toggled, this, &Equalizer::onEnabledToggled);
    connect(m_presetComboBox, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &Equalizer::onPresetChanged);
    connect(m_resetButton, &QPushButton::clicked, this, &Equalizer::onResetClicked);
    
    // Initially disabled
    m_equalizerGroup->setEnabled(false);
}

void Equalizer::setupPresets()
{
    // Add built-in presets
    m_presets.clear();
    
    // Flat (default)
    m_presets.append(EqualizerPreset("Flat", {0, 0, 0, 0, 0, 0, 0, 0, 0, 0}));
    
    // Rock
    m_presets.append(EqualizerPreset("Rock", {5, 3, -2, -3, -1, 2, 5, 7, 8, 8}));
    
    // Pop
    m_presets.append(EqualizerPreset("Pop", {-1, 3, 5, 4, 0, -1, -1, -1, 2, 3}));
    
    // Jazz
    m_presets.append(EqualizerPreset("Jazz", {3, 2, 0, 1, -1, -1, 0, 1, 2, 3}));
    
    // Classical
    m_presets.append(EqualizerPreset("Classical", {4, 3, 2, 1, -1, -1, 0, 2, 3, 4}));
    
    // Bass Boost
    m_presets.append(EqualizerPreset("Bass Boost", {8, 6, 4, 2, 0, -1, -2, -2, -1, 0}));
    
    // Treble Boost
    m_presets.append(EqualizerPreset("Treble Boost", {0, -1, -2, -2, -1, 0, 2, 4, 6, 8}));
    
    // Vocal
    m_presets.append(EqualizerPreset("Vocal", {-2, -1, 1, 3, 4, 4, 3, 1, 0, -1}));
    
    // Electronic
    m_presets.append(EqualizerPreset("Electronic", {4, 3, 0, -2, -1, 0, 1, 4, 6, 7}));
    
    // Update combo box
    m_presetComboBox->clear();
    for (const EqualizerPreset &preset : m_presets) {
        m_presetComboBox->addItem(preset.name);
    }
}

void Equalizer::setEnabled(bool enabled)
{
    if (m_enabled != enabled) {
        m_enabled = enabled;
        m_enabledCheckBox->setChecked(enabled);
        m_equalizerGroup->setEnabled(enabled);
        emit equalizerStateChanged(enabled);
    }
}

void Equalizer::setBandGain(int band, double gain)
{
    if (band >= 0 && band < BAND_COUNT) {
        gain = qBound(MIN_GAIN, gain, MAX_GAIN);
        m_bands[band].gain = gain;

        // Update slider
        int sliderValue = static_cast<int>(gain * 10);
        m_bandSliders[band]->setValue(sliderValue);

        updateBandLabels();
        emit bandGainChanged(band, gain);
    }
}

double Equalizer::getBandGain(int band) const
{
    if (band >= 0 && band < BAND_COUNT) {
        return m_bands[band].gain;
    }
    return 0.0;
}

void Equalizer::applyPreset(const QString &presetName)
{
    for (const EqualizerPreset &preset : m_presets) {
        if (preset.name == presetName) {
            applyPresetGains(preset.gains);
            m_presetComboBox->setCurrentText(presetName);
            emit presetChanged(presetName);
            break;
        }
    }
}

void Equalizer::resetEqualizer()
{
    applyPreset("Flat");
}

QStringList Equalizer::getPresetNames() const
{
    QStringList names;
    for (const EqualizerPreset &preset : m_presets) {
        names.append(preset.name);
    }
    return names;
}

void Equalizer::addCustomPreset(const QString &name, const QList<double> &gains)
{
    if (gains.size() == BAND_COUNT) {
        m_presets.append(EqualizerPreset(name, gains));
        m_presetComboBox->addItem(name);
    }
}

void Equalizer::onBandSliderChanged(int value)
{
    QSlider *slider = qobject_cast<QSlider*>(sender());
    if (slider) {
        int bandIndex = slider->property("bandIndex").toInt();
        double gain = value / 10.0; // Convert back from scaled value

        if (bandIndex >= 0 && bandIndex < BAND_COUNT) {
            m_bands[bandIndex].gain = gain;
            updateBandLabels();
            emit bandGainChanged(bandIndex, gain);
        }
    }
}

void Equalizer::onPresetChanged(const QString &preset)
{
    if (!preset.isEmpty()) {
        applyPreset(preset);
    }
}

void Equalizer::onEnabledToggled(bool enabled)
{
    setEnabled(enabled);
}

void Equalizer::onResetClicked()
{
    resetEqualizer();
}

void Equalizer::updateBandLabels()
{
    for (int i = 0; i < BAND_COUNT; ++i) {
        double gain = m_bands[i].gain;
        QString text = QString("%1dB").arg(gain, 0, 'f', 1);
        if (gain > 0) {
            text = "+" + text;
        }
        m_bandLabels[i]->setText(text);
    }
}

void Equalizer::applyPresetGains(const QList<double> &gains)
{
    if (gains.size() == BAND_COUNT) {
        for (int i = 0; i < BAND_COUNT; ++i) {
            setBandGain(i, gains[i]);
        }
    }
}
