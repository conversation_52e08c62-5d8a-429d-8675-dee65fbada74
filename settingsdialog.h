#ifndef SETTINGSDIALOG_H
#define SETTINGSDIALOG_H

#include <QDialog>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QComboBox>
#include <QSlider>
#include <QLabel>
#include <QPushButton>
#include <QColorDialog>
#include <QFontDialog>
#include <QSettings>

class SettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SettingsDialog(QWidget *parent = nullptr);

    // Settings access
    bool getMinimizeToTray() const;
    bool getStartMinimized() const;
    bool getAutoPlay() const;
    bool getRememberPosition() const;
    int getCrossfadeDuration() const;
    QString getTheme() const;
    
    void setMinimizeToTray(bool enabled);
    void setStartMinimized(bool enabled);
    void setAutoPlay(bool enabled);
    void setRememberPosition(bool enabled);
    void setCrossfadeDuration(int duration);
    void setTheme(const QString &theme);

signals:
    void settingsChanged();

private slots:
    void onAccepted();
    void onRejected();
    void onRestoreDefaults();
    void onThemeChanged();

private:
    void setupUI();
    void setupGeneralTab();
    void setupPlaybackTab();
    void setupAppearanceTab();
    void setupAdvancedTab();
    void loadSettings();
    void saveSettings();
    void restoreDefaults();

    QTabWidget *m_tabWidget;
    
    // General settings
    QCheckBox *m_minimizeToTrayCheck;
    QCheckBox *m_startMinimizedCheck;
    QCheckBox *m_autoPlayCheck;
    QCheckBox *m_rememberPositionCheck;
    
    // Playback settings
    QSpinBox *m_crossfadeSpin;
    QCheckBox *m_gaplessPlaybackCheck;
    QComboBox *m_outputDeviceCombo;
    QSlider *m_bufferSizeSlider;
    
    // Appearance settings
    QComboBox *m_themeCombo;
    QPushButton *m_fontButton;
    QPushButton *m_colorButton;
    QCheckBox *m_showVisualizationCheck;
    
    // Advanced settings
    QCheckBox *m_enableLoggingCheck;
    QSpinBox *m_maxRecentFilesSpin;
    QCheckBox *m_checkUpdatesCheck;
    
    QSettings *m_settings;
};

#endif // SETTINGSDIALOG_H
