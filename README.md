# Modern C++ Audio Player

A feature-rich, modern audio player built with Qt6 and C++ featuring a sleek dark theme and comprehensive playlist management.

## Features

### Core Playback
- Play/Pause/Stop audio playback with modern controls
- Position slider for seeking through tracks
- Volume control with visual feedback
- Time display (current/total) with monospace font
- Support for multiple audio formats (MP3, WAV, OGG, FLAC, M4A, AAC)
- Speed control (25% - 300%)

### Modern UI & Styling
- **Dark theme** with modern styling and smooth animations
- **Responsive layout** with resizable panels
- **Icon-based controls** with tooltips
- **System tray integration** with context menu
- **Tabbed interface** for different features
- **Professional color scheme** with accent colors

### Playlist Management
- **Full playlist support** with drag-and-drop
- **Shuffle and repeat modes** (No Repeat, Repeat All, Repeat One)
- **Add files or entire folders** to playlist
- **Save and load playlists** (M3U format)
- **Visual current track highlighting**
- **Context menu** for playlist operations
- **Double-click to play** tracks

### Audio Enhancement
- **10-band equalizer** with visual sliders
- **Built-in presets** (Rock, Pop, Jazz, Classical, Bass Boost, etc.)
- **Custom preset creation** and management
- **Real-time audio adjustment**

### Advanced Features
- **Keyboard shortcuts** for all major functions
- **System tray minimization** with playback controls
- **Settings dialog** with multiple categories
- **Recent files tracking**
- **Auto-play options**
- **Crossfade support** (framework ready)
- **Gapless playback** options

### System Integration
- **Media key support** (framework ready)
- **System tray notifications**
- **Window state persistence**
- **Multi-file selection**
- **Folder browsing**

## Requirements

- Qt6 (with multimedia module)
- C++17 compatible compiler
- Qt Creator (recommended for development)

## Building the Project

### Using Qt Creator
1. Open Qt Creator
2. Click "Open Project"
3. Navigate to the project directory and select `AudioPlayer.pro`
4. Configure the project with your Qt6 kit
5. Build and run the project

### Using Command Line (with qmake)
```bash
qmake AudioPlayer.pro
make
```

### Using Command Line (with CMake - if you prefer)
You can also convert this to a CMake project by creating a CMakeLists.txt file.

## Usage

1. Launch the application
2. Click "Open File" to select an audio file
3. Use the Play/Pause button to control playback
4. Adjust volume using the volume slider
5. Click and drag the position slider to seek through the track

## Supported Audio Formats

- MP3
- WAV
- OGG
- FLAC
- M4A

## Keyboard Shortcuts

- **Space** - Play/Pause
- **Ctrl+O** - Open File(s)
- **Ctrl+Shift+O** - Open Folder
- **Ctrl+S** - Save Playlist
- **Ctrl+.** - Stop
- **Ctrl+Left/Right** - Previous/Next Track
- **Ctrl+H** - Toggle Shuffle
- **Ctrl+R** - Toggle Repeat
- **Ctrl+E** - Open Equalizer
- **F9** - Toggle Playlist View
- **Ctrl+Q** - Quit

## Project Structure

### Core Files
- `main.cpp` - Application entry point with styling setup
- `mainwindow.h/cpp` - Main window with modern UI and all features
- `audioplayer.h/cpp` - Audio player wrapper class
- `playlist.h/cpp` - Comprehensive playlist management
- `equalizer.h/cpp` - 10-band equalizer with presets
- `settingsdialog.h/cpp` - Settings and preferences dialog

### Resources
- `styles/dark_theme.qss` - Modern dark theme stylesheet
- `icons/*.svg` - Vector icons for all controls
- `resources.qrc` - Qt resource file
- `AudioPlayer.pro` - Qt project file with all dependencies

### UI Files
- `mainwindow.ui` - Main window form (basic template)
- `settingsdialog.ui` - Settings dialog form

## Technical Features

- **Qt6 Multimedia** framework for audio playback
- **Modern C++17** features and best practices
- **Signal-slot architecture** for clean event handling
- **Resource system** for embedded assets
- **Settings persistence** using QSettings
- **Cross-platform compatibility** (Windows, macOS, Linux)
- **Modular design** with separate classes for each feature
- **Memory management** with proper Qt parent-child relationships

## Advanced Usage

### Custom Themes
The application uses QSS (Qt Style Sheets) for theming. You can modify `styles/dark_theme.qss` to customize the appearance.

### Playlist Formats
- Supports M3U playlist format
- Relative and absolute file paths
- Metadata preservation where available

### Audio Formats
Supports all formats available through Qt Multimedia:
- **Lossy**: MP3, AAC, OGG Vorbis
- **Lossless**: FLAC, WAV
- **Container**: M4A, MP4 Audio

### Equalizer Presets
Built-in presets include:
- Flat (default)
- Rock, Pop, Jazz, Classical
- Bass Boost, Treble Boost
- Vocal, Electronic
- Custom presets can be created and saved
