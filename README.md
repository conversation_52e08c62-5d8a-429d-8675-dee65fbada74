# Simple C++ Audio Player

A simple audio player built with Qt6 and C++ that can play various audio formats.

## Features

- Play/Pause/Stop audio playback
- Position slider for seeking through tracks
- Volume control
- Time display (current/total)
- Support for multiple audio formats (MP3, WAV, OGG, FLAC, M4A)
- Clean and intuitive user interface

## Requirements

- Qt6 (with multimedia module)
- C++17 compatible compiler
- Qt Creator (recommended for development)

## Building the Project

### Using Qt Creator
1. Open Qt Creator
2. Click "Open Project"
3. Navigate to the project directory and select `AudioPlayer.pro`
4. Configure the project with your Qt6 kit
5. Build and run the project

### Using Command Line (with qmake)
```bash
qmake AudioPlayer.pro
make
```

### Using Command Line (with CMake - if you prefer)
You can also convert this to a CMake project by creating a CMakeLists.txt file.

## Usage

1. Launch the application
2. Click "Open File" to select an audio file
3. Use the Play/Pause button to control playback
4. Adjust volume using the volume slider
5. Click and drag the position slider to seek through the track

## Supported Audio Formats

- MP3
- WAV
- OGG
- FLAC
- M4A

## Project Structure

- `main.cpp` - Application entry point
- `mainwindow.h/cpp` - Main window class with UI
- `audioplayer.h/cpp` - Audio player wrapper class
- `mainwindow.ui` - UI form file (basic template)
- `AudioPlayer.pro` - Qt project file

## Notes

- This project uses Qt6's multimedia framework
- The UI is created programmatically in the MainWindow class
- The AudioPlayer class wraps QMediaPlayer for easier use
- Make sure you have the Qt multimedia module installed
