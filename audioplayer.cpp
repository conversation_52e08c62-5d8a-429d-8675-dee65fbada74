#include "audioplayer.h"
#include <QUrl>

AudioPlayer::AudioPlayer(QObject *parent)
    : QObject(parent)
    , m_mediaPlayer(new QMediaPlayer(this))
    , m_audioOutput(new QAudioOutput(this))
{
    // Set up audio output
    m_mediaPlayer->setAudioOutput(m_audioOutput);
    
    // Connect signals
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged, 
            this, &AudioPlayer::handlePositionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged, 
            this, &AudioPlayer::handleDurationChanged);
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged, 
            this, &AudioPlayer::handlePlaybackStateChanged);
}

AudioPlayer::~AudioPlayer()
{
}

void AudioPlayer::setSource(const QString &filePath)
{
    m_mediaPlayer->setSource(QUrl::fromLocalFile(filePath));
}

void AudioPlayer::play()
{
    m_mediaPlayer->play();
}

void AudioPlayer::pause()
{
    m_mediaPlayer->pause();
}

void AudioPlayer::stop()
{
    m_mediaPlayer->stop();
}

void AudioPlayer::setPosition(qint64 position)
{
    m_mediaPlayer->setPosition(position);
}

void AudioPlayer::setVolume(int volume)
{
    // Convert percentage to float (0.0 - 1.0)
    float volumeLevel = volume / 100.0f;
    m_audioOutput->setVolume(volumeLevel);
}

bool AudioPlayer::isPlaying() const
{
    return m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState;
}

qint64 AudioPlayer::position() const
{
    return m_mediaPlayer->position();
}

qint64 AudioPlayer::duration() const
{
    return m_mediaPlayer->duration();
}

void AudioPlayer::handlePositionChanged(qint64 position)
{
    emit positionChanged(position);
}

void AudioPlayer::handleDurationChanged(qint64 duration)
{
    emit durationChanged(duration);
}

void AudioPlayer::handlePlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    Q_UNUSED(state)
    emit playbackStateChanged();
}

void AudioPlayer::handlePlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    Q_UNUSED(state)
    emit playbackStateChanged();
}
