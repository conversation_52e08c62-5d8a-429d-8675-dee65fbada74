/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // dark_theme.qss
  0x0,0x0,0x5,0x1b,
  0x0,
  0x0,0x16,0x7a,0x78,0xda,0xbd,0x58,0xdb,0x6e,0xe3,0x36,0x10,0x7d,0xcf,0x57,0x10,
  0xeb,0x97,0x36,0x88,0x10,0xd9,0x71,0xbc,0x29,0xfb,0x94,0x74,0x81,0x62,0x81,0xa6,
  0x48,0x90,0x74,0xf7,0x99,0x92,0x68,0x9b,0x88,0x4c,0xa,0x14,0x95,0x64,0x77,0xb1,
  0xff,0xde,0xa1,0x78,0x11,0x65,0x89,0xbe,0xa4,0x41,0x4d,0x20,0x10,0x29,0x71,0x2e,
  0x67,0xe,0x67,0x86,0x39,0x3f,0x45,0xb7,0xa2,0xa0,0x92,0xa3,0x4f,0x44,0x3e,0xa1,
  0xc7,0x35,0xdd,0x50,0xb4,0x14,0x12,0x5d,0x37,0x5,0x13,0xe8,0xae,0x24,0xdf,0xa8,
  0x44,0xa7,0xe7,0x27,0x27,0xf7,0xb7,0x84,0xf1,0xaf,0x8c,0x17,0xe2,0x5,0xfd,0x38,
  0x41,0xf0,0xcb,0x48,0xfe,0xb4,0x92,0xa2,0xe1,0x45,0x92,0x8b,0x52,0x48,0x8c,0x26,
  0x53,0xaa,0xc7,0xef,0xed,0x6b,0xb7,0xb6,0x6c,0x7f,0x66,0x6d,0x29,0xb8,0x4a,0x96,
  0x64,0xc3,0xca,0x6f,0x18,0x7d,0x78,0xa0,0x2b,0x41,0xd1,0x3f,0x9f,0x3f,0x9c,0xa1,
  0x6b,0xc9,0x48,0x79,0x86,0x6a,0xc2,0xeb,0xa4,0xa6,0x92,0xc1,0xf7,0x3f,0x41,0xe7,
  0x57,0x56,0xac,0xa8,0xfa,0x8f,0xfa,0x6a,0xf6,0x9d,0x62,0x34,0x4d,0x2b,0xd5,0xa,
  0x3d,0x3f,0x45,0x37,0x8d,0x52,0x82,0xd7,0xda,0xaf,0xfb,0xbb,0xa6,0x5e,0x9b,0x79,
  0x5c,0xcd,0xac,0xd0,0xc3,0x88,0xcc,0x84,0x4,0xbc,0x40,0x5e,0xf5,0x8a,0x6a,0x51,
  0xb2,0x2,0x4d,0xe6,0xa9,0x1e,0xe1,0xeb,0x44,0x92,0x82,0x35,0x35,0x46,0x8b,0xea,
  0xd5,0xac,0x57,0xa4,0x28,0x18,0x5f,0x61,0x74,0x5,0xfb,0xa6,0x7e,0x39,0x6a,0xf4,
  0xb,0x65,0xab,0xb5,0xc2,0xe8,0x32,0xb5,0x82,0x37,0x8c,0x27,0x6b,0xbb,0x38,0x4b,
  0xf5,0x7e,0xd,0x50,0x67,0x3d,0x5e,0x8b,0x67,0x8,0x55,0xd4,0x87,0x8b,0x42,0x8f,
  0x9e,0x91,0xee,0xd5,0x65,0xaa,0xc7,0x40,0x60,0x25,0x69,0x5d,0xd3,0x62,0x7,0xfa,
  0x85,0x1e,0xa3,0x22,0x2f,0x52,0x3d,0x6,0x22,0xb,0x56,0x93,0xac,0xdc,0x29,0x93,
  0xe8,0xd1,0x7,0x67,0xd1,0xfe,0x46,0xf5,0xcc,0x88,0x1e,0x2e,0xae,0x7f,0x0,0x72,
  0x52,0x94,0x91,0xf8,0x4e,0x2a,0x60,0xf3,0x1d,0x69,0x6a,0x6a,0xe6,0x67,0x28,0x7c,
  0x59,0x2b,0x51,0xb9,0xf5,0xde,0x2e,0x4e,0x5f,0xd5,0xd8,0x6,0x80,0xe7,0x99,0x89,
  0xa6,0xde,0x47,0x9e,0x34,0xfd,0x78,0x55,0xcc,0xfb,0xe4,0xe1,0x82,0xd3,0x51,0xbe,
  0x98,0xc8,0xba,0x78,0xbf,0xb0,0x42,0xad,0x31,0x9a,0xf7,0x16,0x1d,0x9,0xba,0xd5,
  0x90,0xe5,0xf3,0x21,0x33,0xb6,0xfd,0x36,0x4c,0x89,0x79,0x6f,0xdf,0x46,0x20,0x18,
  0xdb,0xdb,0x7,0x62,0x1f,0xf,0xa7,0xe9,0x82,0x66,0x74,0x60,0xa3,0xa8,0x28,0xdf,
  0x87,0xe4,0x34,0xfd,0x98,0x4f,0xd3,0x31,0x24,0xa3,0xc2,0xf6,0x59,0x93,0xd2,0x5,
  0x4d,0xa9,0xe3,0xcf,0x3,0x1c,0x67,0x2a,0xd,0x6f,0xcc,0x33,0xc6,0xb0,0x1,0x44,
  0x80,0x1c,0xc9,0xbe,0x3,0xce,0xa4,0x74,0xc2,0x6,0xa1,0x74,0x81,0xf1,0x87,0xbb,
  0xd3,0x87,0x77,0x26,0x89,0xb,0x17,0x33,0xa7,0x73,0x4d,0x78,0x51,0x8e,0xe9,0xc,
  0x5,0xee,0xe6,0x95,0xa5,0x4e,0x97,0x69,0x9c,0x75,0xdd,0xca,0x96,0x15,0x57,0x9e,
  0x64,0x44,0xae,0x18,0xc7,0x28,0xb9,0x84,0x54,0x95,0xee,0xb1,0x2c,0x2,0xf0,0x56,
  0xa0,0xdd,0xf6,0xba,0xc9,0x92,0x8a,0xac,0x8e,0x77,0x6d,0x80,0x15,0x44,0xeb,0x8b,
  0x28,0x1b,0xa8,0x56,0x46,0x76,0x10,0xb3,0xc9,0x73,0xfb,0x62,0x5f,0x0,0xfd,0x39,
  0x3a,0x36,0x5c,0xb3,0x7e,0xb8,0xb6,0xd4,0xc5,0x62,0xe7,0x2,0x32,0x1b,0x4,0x64,
  0x16,0x9,0xc8,0x62,0x10,0x90,0xb9,0xf,0x8,0xb8,0xff,0x17,0xc9,0x68,0x69,0xb8,
  0xda,0x3e,0x5a,0x3d,0x63,0x15,0x25,0xf4,0x4c,0x49,0xa8,0xb1,0x15,0x91,0x94,0x9b,
  0x72,0x68,0x36,0x4f,0x96,0xac,0xa4,0x7f,0x93,0xd,0xd,0x45,0xf5,0xf3,0x8a,0x1a,
  0xa9,0x4e,0xb,0x57,0x9d,0xc6,0xb4,0xfa,0x92,0x37,0xf5,0xe5,0xca,0xe8,0xca,0x1b,
  0xa9,0xd5,0x3f,0x32,0xab,0xe,0xd2,0x89,0x79,0xa1,0x4,0xe0,0xe5,0x97,0x43,0x2b,
  0x7c,0xcf,0x0,0x29,0x1e,0xaa,0x2e,0xa9,0xa1,0x67,0xd8,0x8,0x2e,0xc0,0x93,0x9c,
  0xe,0xb2,0xe0,0x6f,0xce,0x58,0x67,0x56,0xde,0xfe,0x22,0x89,0xd5,0xc2,0xc9,0x6a,
  0x85,0x6c,0xbf,0xf1,0x8b,0xee,0x80,0x74,0xd6,0x2c,0x61,0xf1,0x57,0x3,0x31,0x3c,
  0xed,0xeb,0x46,0x66,0x97,0x7a,0xbc,0xa5,0x4d,0xf0,0x1c,0x24,0xa5,0x82,0x66,0x8c,
  0x28,0x9a,0x8c,0xc9,0xb7,0xc5,0x4e,0x7f,0x58,0xd3,0x92,0xe6,0x8a,0x9,0x9e,0xec,
  0x29,0x39,0xa2,0x51,0x25,0xe3,0x34,0xcc,0x94,0x9d,0x2f,0x18,0x33,0x45,0x37,0xd6,
  0xa3,0xb0,0x45,0xe9,0x59,0x99,0x9,0x48,0xa5,0x9b,0x9e,0x2f,0x17,0xed,0x6f,0x5c,
  0x1c,0x36,0xb6,0xed,0xaa,0xf2,0xa1,0x81,0xdb,0xd4,0x19,0x15,0x79,0x60,0x73,0x63,
  0x42,0xf9,0x27,0xbc,0xac,0xd0,0x8d,0x78,0x6d,0x23,0xd7,0xce,0xf4,0xe4,0xc7,0xe,
  0xfe,0xbe,0x31,0x5c,0xe6,0x64,0x26,0x50,0x3c,0x1d,0xcb,0x3,0x20,0xc3,0x65,0xed,
  0x94,0x33,0x4,0x63,0xc5,0x54,0x49,0xad,0x3d,0x90,0x12,0x73,0xd3,0xb6,0x24,0x90,
  0x32,0xda,0x73,0x6e,0xa4,0x1a,0x59,0x25,0x5d,0xaa,0x11,0xd9,0x18,0xa5,0xa8,0x4d,
  0xcf,0xfa,0x6f,0x14,0x47,0xc0,0xe2,0x91,0x64,0x8e,0xd5,0x1a,0xc,0x98,0x3a,0x5c,
  0x2b,0xc2,0xe9,0x56,0x2d,0x8b,0x79,0x1f,0x6f,0xbc,0xb5,0x5f,0x20,0xf3,0x86,0x40,
  0xe6,0x53,0xa0,0xea,0x3d,0x5a,0xe8,0x48,0xab,0x6c,0xb1,0x96,0xb6,0x5,0x76,0xa8,
  0x6,0xda,0xdf,0x81,0x78,0xa1,0xb4,0xa3,0x38,0xf7,0x90,0x43,0x4,0xa1,0xf3,0x24,
  0xb6,0x12,0xb5,0x53,0x2d,0xa,0x64,0x28,0x96,0x8f,0xd7,0xb9,0x10,0x94,0x41,0x7d,
  0x18,0xab,0x6,0x6d,0xd5,0xf1,0xa2,0x5d,0xad,0xd9,0xa5,0xc2,0xf5,0xf6,0x3b,0xeb,
  0xcb,0xe8,0xd5,0x22,0xae,0x27,0x5e,0xf3,0x17,0xa9,0x1e,0xe,0x93,0x5b,0xca,0x1b,
  0x8f,0x88,0x9e,0xe8,0xe7,0x37,0xdf,0xe5,0xe2,0xc9,0xc8,0x91,0x47,0x5b,0x6d,0xd5,
  0x8c,0x27,0x36,0x5d,0x3f,0xaf,0xc6,0xca,0xfd,0xa0,0x28,0xf6,0xc4,0x1c,0xc1,0x2b,
  0xb7,0xf7,0x5d,0xe,0xc2,0x18,0x41,0xb5,0xf0,0x71,0xe7,0x20,0x9c,0x41,0xf4,0xba,
  0xef,0x8e,0xb3,0x5e,0x53,0x59,0x11,0xd5,0xd4,0x1d,0x95,0xdb,0xe9,0xc1,0xa1,0xb3,
  0x61,0x32,0xb9,0x6f,0xb7,0x5f,0xae,0x2a,0xbb,0xcb,0xdb,0x26,0x13,0x3e,0x6b,0xb7,
  0xb3,0x2e,0x6b,0xbf,0xeb,0xa5,0x7c,0x3e,0x48,0xa6,0x3d,0x62,0x4,0xd,0xc2,0x34,
  0xf5,0x80,0x3a,0x83,0xfa,0xe4,0x8f,0x5f,0xa4,0xfd,0xf7,0xb8,0x90,0xa2,0x4a,0xa,
  0xf1,0xc2,0xa3,0x37,0x7,0xab,0x6d,0x36,0x54,0x86,0xf5,0xbe,0x84,0x48,0xe9,0xff,
  0xeb,0xc2,0x36,0xba,0x7f,0x1e,0xde,0x20,0x4d,0xa1,0x98,0x7b,0x0,0x7a,0x94,0xe,
  0x41,0xf0,0x5d,0xef,0x9e,0xf,0xdb,0x8,0x76,0x9f,0xf5,0x58,0xe8,0xa3,0x73,0x7f,
  0x9d,0xd5,0x20,0x20,0x57,0x9f,0x81,0x69,0x5f,0x18,0x7d,0x79,0x97,0x78,0x1d,0xd4,
  0xdc,0x58,0xb2,0x56,0x8c,0x7b,0xd2,0xe8,0x9,0x3c,0x43,0x3f,0xf9,0x49,0x34,0x59,
  0x49,0xed,0xfc,0x7f,0xe3,0x90,0x4d,0x9c,0x46,0xab,0xbf,0x2a,0xf7,0x6c,0x39,0x90,
  0x3e,0xfa,0x3c,0xac,0x69,0xfe,0xd4,0x9d,0x7,0x3d,0xeb,0x7c,0xd1,0x7d,0x6f,0xd7,
  0xab,0xb5,0x21,0xb1,0x1f,0xc0,0xa1,0xe7,0x5,0x64,0x69,0x25,0xe4,0xd6,0xcd,0x63,
  0xef,0x55,0xf0,0x50,0xdf,0x2f,0x86,0x29,0x74,0x80,0x68,0xc4,0x24,0x9c,0xeb,0xb5,
  0x43,0x2b,0xf4,0x16,0x3e,0x61,0x92,0xdd,0x21,0xfb,0x88,0xff,0x40,0x0,0xcc,0x77,
  0x52,0xac,0xf4,0x3f,0xba,0x7c,0xbe,0x73,0xb,0x41,0xc6,0x7b,0x1b,0x3a,0x8a,0xbe,
  0xaa,0x84,0x94,0x6c,0x5,0x2d,0x5d,0xe,0xa7,0x8b,0xca,0x83,0x50,0xb,0xf4,0x63,
  0x70,0xa9,0xe1,0x4f,0x47,0x81,0xb5,0x7d,0x4b,0xd5,0x3d,0xa0,0x10,0x25,0x7a,0x64,
  0x95,0xe9,0x0,0x61,0xa2,0x9f,0xf,0x3a,0x13,0xf1,0x3a,0xbc,0xbf,0x7b,0x9b,0x47,
  0x1a,0x19,0x7b,0x4e,0xfe,0x5,0x81,0xc9,0xe6,0x79,
    // play.svg
  0x0,0x0,0x0,0x84,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,0x35,0x76,
  0x31,0x34,0x6c,0x31,0x31,0x2d,0x37,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // folder.svg
  0x0,0x0,0x0,0xd3,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x30,0x20,0x34,
  0x48,0x34,0x63,0x2d,0x31,0x2e,0x31,0x31,0x20,0x30,0x2d,0x32,0x20,0x2e,0x38,0x39,
  0x2d,0x32,0x20,0x32,0x76,0x31,0x32,0x63,0x30,0x20,0x31,0x2e,0x31,0x31,0x2e,0x38,
  0x39,0x20,0x32,0x20,0x32,0x20,0x32,0x68,0x31,0x36,0x63,0x31,0x2e,0x31,0x31,0x20,
  0x30,0x20,0x32,0x2d,0x2e,0x38,0x39,0x20,0x32,0x2d,0x32,0x56,0x38,0x63,0x30,0x2d,
  0x31,0x2e,0x31,0x31,0x2d,0x2e,0x38,0x39,0x2d,0x32,0x2d,0x32,0x2d,0x32,0x68,0x2d,
  0x38,0x6c,0x2d,0x32,0x2d,0x32,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // playlist.svg
  0x0,0x0,0x0,0xf7,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x20,0x36,
  0x48,0x33,0x76,0x32,0x68,0x31,0x32,0x56,0x36,0x7a,0x6d,0x30,0x20,0x34,0x48,0x33,
  0x76,0x32,0x68,0x31,0x32,0x76,0x2d,0x32,0x7a,0x4d,0x33,0x20,0x31,0x36,0x68,0x38,
  0x76,0x2d,0x32,0x48,0x33,0x76,0x32,0x7a,0x4d,0x31,0x37,0x20,0x36,0x76,0x38,0x2e,
  0x31,0x38,0x63,0x2d,0x2e,0x33,0x31,0x2d,0x2e,0x31,0x31,0x2d,0x2e,0x36,0x35,0x2d,
  0x2e,0x31,0x38,0x2d,0x31,0x2d,0x2e,0x31,0x38,0x2d,0x31,0x2e,0x36,0x36,0x20,0x30,
  0x2d,0x33,0x20,0x31,0x2e,0x33,0x34,0x2d,0x33,0x20,0x33,0x73,0x31,0x2e,0x33,0x34,
  0x20,0x33,0x20,0x33,0x20,0x33,0x20,0x33,0x2d,0x31,0x2e,0x33,0x34,0x20,0x33,0x2d,
  0x33,0x56,0x38,0x68,0x33,0x56,0x36,0x68,0x2d,0x35,0x7a,0x22,0x2f,0x3e,0xa,0x3c,
  0x2f,0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x0,0x96,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x36,0x20,0x31,0x39,
  0x68,0x34,0x56,0x35,0x48,0x36,0x76,0x31,0x34,0x7a,0x6d,0x38,0x2d,0x31,0x34,0x76,
  0x31,0x34,0x68,0x34,0x56,0x35,0x68,0x2d,0x34,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // settings.svg
  0x0,0x0,0x3,0xd3,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x31,
  0x34,0x2c,0x31,0x32,0x2e,0x39,0x34,0x63,0x30,0x2e,0x30,0x34,0x2d,0x30,0x2e,0x33,
  0x2c,0x30,0x2e,0x30,0x36,0x2d,0x30,0x2e,0x36,0x31,0x2c,0x30,0x2e,0x30,0x36,0x2d,
  0x30,0x2e,0x39,0x34,0x63,0x30,0x2d,0x30,0x2e,0x33,0x32,0x2d,0x30,0x2e,0x30,0x32,
  0x2d,0x30,0x2e,0x36,0x34,0x2d,0x30,0x2e,0x30,0x37,0x2d,0x30,0x2e,0x39,0x34,0x6c,
  0x32,0x2e,0x30,0x33,0x2d,0x31,0x2e,0x35,0x38,0x63,0x30,0x2e,0x31,0x38,0x2d,0x30,
  0x2e,0x31,0x34,0x2c,0x30,0x2e,0x32,0x33,0x2d,0x30,0x2e,0x34,0x31,0x2c,0x30,0x2e,
  0x31,0x32,0x2d,0x30,0x2e,0x36,0x31,0x20,0x6c,0x2d,0x31,0x2e,0x39,0x32,0x2d,0x33,
  0x2e,0x33,0x32,0x63,0x2d,0x30,0x2e,0x31,0x32,0x2d,0x30,0x2e,0x32,0x32,0x2d,0x30,
  0x2e,0x33,0x37,0x2d,0x30,0x2e,0x32,0x39,0x2d,0x30,0x2e,0x35,0x39,0x2d,0x30,0x2e,
  0x32,0x32,0x6c,0x2d,0x32,0x2e,0x33,0x39,0x2c,0x30,0x2e,0x39,0x36,0x63,0x2d,0x30,
  0x2e,0x35,0x2d,0x30,0x2e,0x33,0x38,0x2d,0x31,0x2e,0x30,0x33,0x2d,0x30,0x2e,0x37,
  0x2d,0x31,0x2e,0x36,0x32,0x2d,0x30,0x2e,0x39,0x34,0x4c,0x31,0x34,0x2e,0x34,0x2c,
  0x32,0x2e,0x38,0x31,0x63,0x2d,0x30,0x2e,0x30,0x34,0x2d,0x30,0x2e,0x32,0x34,0x2d,
  0x30,0x2e,0x32,0x34,0x2d,0x30,0x2e,0x34,0x31,0x2d,0x30,0x2e,0x34,0x38,0x2d,0x30,
  0x2e,0x34,0x31,0x20,0x68,0x2d,0x33,0x2e,0x38,0x34,0x63,0x2d,0x30,0x2e,0x32,0x34,
  0x2c,0x30,0x2d,0x30,0x2e,0x34,0x33,0x2c,0x30,0x2e,0x31,0x37,0x2d,0x30,0x2e,0x34,
  0x37,0x2c,0x30,0x2e,0x34,0x31,0x4c,0x39,0x2e,0x32,0x35,0x2c,0x35,0x2e,0x33,0x35,
  0x43,0x38,0x2e,0x36,0x36,0x2c,0x35,0x2e,0x35,0x39,0x2c,0x38,0x2e,0x31,0x32,0x2c,
  0x35,0x2e,0x39,0x32,0x2c,0x37,0x2e,0x36,0x33,0x2c,0x36,0x2e,0x32,0x39,0x4c,0x35,
  0x2e,0x32,0x34,0x2c,0x35,0x2e,0x33,0x33,0x63,0x2d,0x30,0x2e,0x32,0x32,0x2d,0x30,
  0x2e,0x30,0x38,0x2d,0x30,0x2e,0x34,0x37,0x2c,0x30,0x2d,0x30,0x2e,0x35,0x39,0x2c,
  0x30,0x2e,0x32,0x32,0x4c,0x32,0x2e,0x37,0x34,0x2c,0x38,0x2e,0x38,0x37,0x20,0x43,
  0x32,0x2e,0x36,0x32,0x2c,0x39,0x2e,0x30,0x38,0x2c,0x32,0x2e,0x36,0x36,0x2c,0x39,
  0x2e,0x33,0x34,0x2c,0x32,0x2e,0x38,0x36,0x2c,0x39,0x2e,0x34,0x38,0x6c,0x32,0x2e,
  0x30,0x33,0x2c,0x31,0x2e,0x35,0x38,0x43,0x34,0x2e,0x38,0x34,0x2c,0x31,0x31,0x2e,
  0x33,0x36,0x2c,0x34,0x2e,0x38,0x32,0x2c,0x31,0x31,0x2e,0x36,0x39,0x2c,0x34,0x2e,
  0x38,0x32,0x2c,0x31,0x32,0x73,0x30,0x2e,0x30,0x32,0x2c,0x30,0x2e,0x36,0x34,0x2c,
  0x30,0x2e,0x30,0x37,0x2c,0x30,0x2e,0x39,0x34,0x6c,0x2d,0x32,0x2e,0x30,0x33,0x2c,
  0x31,0x2e,0x35,0x38,0x20,0x63,0x2d,0x30,0x2e,0x31,0x38,0x2c,0x30,0x2e,0x31,0x34,
  0x2d,0x30,0x2e,0x32,0x33,0x2c,0x30,0x2e,0x34,0x31,0x2d,0x30,0x2e,0x31,0x32,0x2c,
  0x30,0x2e,0x36,0x31,0x6c,0x31,0x2e,0x39,0x32,0x2c,0x33,0x2e,0x33,0x32,0x63,0x30,
  0x2e,0x31,0x32,0x2c,0x30,0x2e,0x32,0x32,0x2c,0x30,0x2e,0x33,0x37,0x2c,0x30,0x2e,
  0x32,0x39,0x2c,0x30,0x2e,0x35,0x39,0x2c,0x30,0x2e,0x32,0x32,0x6c,0x32,0x2e,0x33,
  0x39,0x2d,0x30,0x2e,0x39,0x36,0x63,0x30,0x2e,0x35,0x2c,0x30,0x2e,0x33,0x38,0x2c,
  0x31,0x2e,0x30,0x33,0x2c,0x30,0x2e,0x37,0x2c,0x31,0x2e,0x36,0x32,0x2c,0x30,0x2e,
  0x39,0x34,0x6c,0x30,0x2e,0x33,0x36,0x2c,0x32,0x2e,0x35,0x34,0x20,0x63,0x30,0x2e,
  0x30,0x35,0x2c,0x30,0x2e,0x32,0x34,0x2c,0x30,0x2e,0x32,0x34,0x2c,0x30,0x2e,0x34,
  0x31,0x2c,0x30,0x2e,0x34,0x38,0x2c,0x30,0x2e,0x34,0x31,0x68,0x33,0x2e,0x38,0x34,
  0x63,0x30,0x2e,0x32,0x34,0x2c,0x30,0x2c,0x30,0x2e,0x34,0x34,0x2d,0x30,0x2e,0x31,
  0x37,0x2c,0x30,0x2e,0x34,0x37,0x2d,0x30,0x2e,0x34,0x31,0x6c,0x30,0x2e,0x33,0x36,
  0x2d,0x32,0x2e,0x35,0x34,0x63,0x30,0x2e,0x35,0x39,0x2d,0x30,0x2e,0x32,0x34,0x2c,
  0x31,0x2e,0x31,0x33,0x2d,0x30,0x2e,0x35,0x36,0x2c,0x31,0x2e,0x36,0x32,0x2d,0x30,
  0x2e,0x39,0x34,0x6c,0x32,0x2e,0x33,0x39,0x2c,0x30,0x2e,0x39,0x36,0x20,0x63,0x30,
  0x2e,0x32,0x32,0x2c,0x30,0x2e,0x30,0x38,0x2c,0x30,0x2e,0x34,0x37,0x2c,0x30,0x2c,
  0x30,0x2e,0x35,0x39,0x2d,0x30,0x2e,0x32,0x32,0x6c,0x31,0x2e,0x39,0x32,0x2d,0x33,
  0x2e,0x33,0x32,0x63,0x30,0x2e,0x31,0x32,0x2d,0x30,0x2e,0x32,0x32,0x2c,0x30,0x2e,
  0x30,0x37,0x2d,0x30,0x2e,0x34,0x37,0x2d,0x30,0x2e,0x31,0x32,0x2d,0x30,0x2e,0x36,
  0x31,0x4c,0x31,0x39,0x2e,0x31,0x34,0x2c,0x31,0x32,0x2e,0x39,0x34,0x7a,0x20,0x4d,
  0x31,0x32,0x2c,0x31,0x35,0x2e,0x36,0x63,0x2d,0x31,0x2e,0x39,0x38,0x2c,0x30,0x2d,
  0x33,0x2e,0x36,0x2d,0x31,0x2e,0x36,0x32,0x2d,0x33,0x2e,0x36,0x2d,0x33,0x2e,0x36,
  0x20,0x73,0x31,0x2e,0x36,0x32,0x2d,0x33,0x2e,0x36,0x2c,0x33,0x2e,0x36,0x2d,0x33,
  0x2e,0x36,0x73,0x33,0x2e,0x36,0x2c,0x31,0x2e,0x36,0x32,0x2c,0x33,0x2e,0x36,0x2c,
  0x33,0x2e,0x36,0x53,0x31,0x33,0x2e,0x39,0x38,0x2c,0x31,0x35,0x2e,0x36,0x2c,0x31,
  0x32,0x2c,0x31,0x35,0x2e,0x36,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // equalizer.svg
  0x0,0x0,0x0,0xa7,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x30,0x20,0x32,
  0x30,0x68,0x34,0x56,0x34,0x68,0x2d,0x34,0x76,0x31,0x36,0x7a,0x6d,0x2d,0x36,0x20,
  0x30,0x68,0x34,0x76,0x2d,0x38,0x48,0x34,0x76,0x38,0x7a,0x4d,0x31,0x36,0x20,0x39,
  0x76,0x31,0x31,0x68,0x34,0x56,0x39,0x68,0x2d,0x34,0x7a,0x22,0x2f,0x3e,0xa,0x3c,
  0x2f,0x73,0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x0,0x92,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x36,0x20,0x36,0x68,
  0x32,0x76,0x31,0x32,0x48,0x36,0x7a,0x6d,0x33,0x2e,0x35,0x20,0x36,0x6c,0x38,0x2e,
  0x35,0x20,0x36,0x56,0x36,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
  0xa,
    // repeat.svg
  0x0,0x0,0x0,0xb6,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x37,0x20,0x37,0x68,
  0x31,0x30,0x76,0x33,0x6c,0x34,0x2d,0x34,0x2d,0x34,0x2d,0x34,0x76,0x33,0x48,0x35,
  0x76,0x36,0x68,0x32,0x56,0x37,0x7a,0x6d,0x31,0x30,0x20,0x31,0x30,0x48,0x37,0x76,
  0x2d,0x33,0x6c,0x2d,0x34,0x20,0x34,0x20,0x34,0x20,0x34,0x76,0x2d,0x33,0x68,0x31,
  0x32,0x76,0x2d,0x36,0x68,0x2d,0x32,0x76,0x34,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x1,0x32,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x33,0x20,0x39,0x76,
  0x36,0x68,0x34,0x6c,0x35,0x20,0x35,0x56,0x34,0x4c,0x37,0x20,0x39,0x48,0x33,0x7a,
  0x6d,0x31,0x33,0x2e,0x35,0x20,0x33,0x63,0x30,0x2d,0x31,0x2e,0x37,0x37,0x2d,0x31,
  0x2e,0x30,0x32,0x2d,0x33,0x2e,0x32,0x39,0x2d,0x32,0x2e,0x35,0x2d,0x34,0x2e,0x30,
  0x33,0x76,0x38,0x2e,0x30,0x35,0x63,0x31,0x2e,0x34,0x38,0x2d,0x2e,0x37,0x33,0x20,
  0x32,0x2e,0x35,0x2d,0x32,0x2e,0x32,0x35,0x20,0x32,0x2e,0x35,0x2d,0x34,0x2e,0x30,
  0x32,0x7a,0x4d,0x31,0x34,0x20,0x33,0x2e,0x32,0x33,0x76,0x32,0x2e,0x30,0x36,0x63,
  0x32,0x2e,0x38,0x39,0x2e,0x38,0x36,0x20,0x35,0x20,0x33,0x2e,0x35,0x34,0x20,0x35,
  0x20,0x36,0x2e,0x37,0x31,0x73,0x2d,0x32,0x2e,0x31,0x31,0x20,0x35,0x2e,0x38,0x35,
  0x2d,0x35,0x20,0x36,0x2e,0x37,0x31,0x76,0x32,0x2e,0x30,0x36,0x63,0x34,0x2e,0x30,
  0x31,0x2d,0x2e,0x39,0x31,0x20,0x37,0x2d,0x34,0x2e,0x34,0x39,0x20,0x37,0x2d,0x38,
  0x2e,0x37,0x37,0x73,0x2d,0x32,0x2e,0x39,0x39,0x2d,0x37,0x2e,0x38,0x36,0x2d,0x37,
  0x2d,0x38,0x2e,0x37,0x37,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,
  0xa,
    // next.svg
  0x0,0x0,0x0,0x9a,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x36,0x20,0x31,0x38,
  0x6c,0x38,0x2e,0x35,0x2d,0x36,0x4c,0x36,0x20,0x36,0x76,0x31,0x32,0x7a,0x4d,0x31,
  0x36,0x20,0x36,0x76,0x31,0x32,0x68,0x32,0x56,0x36,0x68,0x2d,0x32,0x7a,0x22,0x2f,
  0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // shuffle.svg
  0x0,0x0,0x1,0x24,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x30,0x2e,0x35,
  0x39,0x20,0x39,0x2e,0x31,0x37,0x4c,0x35,0x2e,0x34,0x31,0x20,0x34,0x20,0x34,0x20,
  0x35,0x2e,0x34,0x31,0x6c,0x35,0x2e,0x31,0x37,0x20,0x35,0x2e,0x31,0x37,0x20,0x31,
  0x2e,0x34,0x32,0x2d,0x31,0x2e,0x34,0x31,0x7a,0x4d,0x31,0x34,0x2e,0x35,0x20,0x34,
  0x6c,0x32,0x2e,0x30,0x34,0x20,0x32,0x2e,0x30,0x34,0x4c,0x34,0x20,0x31,0x38,0x2e,
  0x35,0x39,0x20,0x35,0x2e,0x34,0x31,0x20,0x32,0x30,0x20,0x31,0x37,0x2e,0x39,0x36,
  0x20,0x37,0x2e,0x34,0x36,0x20,0x32,0x30,0x20,0x39,0x2e,0x35,0x56,0x34,0x68,0x2d,
  0x35,0x2e,0x35,0x7a,0x6d,0x2e,0x33,0x33,0x20,0x39,0x2e,0x34,0x31,0x6c,0x2d,0x31,
  0x2e,0x34,0x31,0x20,0x31,0x2e,0x34,0x31,0x20,0x33,0x2e,0x31,0x33,0x20,0x33,0x2e,
  0x31,0x33,0x4c,0x31,0x34,0x2e,0x35,0x20,0x32,0x30,0x48,0x32,0x30,0x76,0x2d,0x35,
  0x2e,0x35,0x6c,0x2d,0x32,0x2e,0x30,0x34,0x20,0x32,0x2e,0x30,0x34,0x2d,0x33,0x2e,
  0x31,0x33,0x2d,0x33,0x2e,0x31,0x33,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0x84,
  0x3c,
  0x73,0x76,0x67,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,
  0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,
  0x30,0x2f,0x73,0x76,0x67,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,0x22,0x20,0x76,0x69,
  0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,0x34,0x20,0x32,0x34,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x3e,0xa,
  0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x36,0x20,0x36,0x68,
  0x31,0x32,0x76,0x31,0x32,0x48,0x36,0x7a,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // styles
  0x0,0x6,
  0x7,0xac,0x2,0xc3,
  0x0,0x73,
  0x0,0x74,0x0,0x79,0x0,0x6c,0x0,0x65,0x0,0x73,
    // dark_theme.qss
  0x0,0xe,
  0xd,0x16,0x97,0xc3,
  0x0,0x64,
  0x0,0x61,0x0,0x72,0x0,0x6b,0x0,0x5f,0x0,0x74,0x0,0x68,0x0,0x65,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x71,0x0,0x73,0x0,0x73,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // folder.svg
  0x0,0xa,
  0xa,0xc8,0xf6,0x87,
  0x0,0x66,
  0x0,0x6f,0x0,0x6c,0x0,0x64,0x0,0x65,0x0,0x72,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // playlist.svg
  0x0,0xc,
  0xe,0x42,0x7a,0xa7,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x6c,0x0,0x69,0x0,0x73,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // settings.svg
  0x0,0xc,
  0xb,0xdf,0x2c,0xc7,
  0x0,0x73,
  0x0,0x65,0x0,0x74,0x0,0x74,0x0,0x69,0x0,0x6e,0x0,0x67,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // equalizer.svg
  0x0,0xd,
  0x9,0xd2,0x5d,0x47,
  0x0,0x65,
  0x0,0x71,0x0,0x75,0x0,0x61,0x0,0x6c,0x0,0x69,0x0,0x7a,0x0,0x65,0x0,0x72,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // repeat.svg
  0x0,0xa,
  0xb,0x88,0x42,0x7,
  0x0,0x72,
  0x0,0x65,0x0,0x70,0x0,0x65,0x0,0x61,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // shuffle.svg
  0x0,0xb,
  0xd,0xd7,0xad,0x47,
  0x0,0x73,
  0x0,0x68,0x0,0x75,0x0,0x66,0x0,0x66,0x0,0x6c,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xc,0x0,0x0,0x0,0x4,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles
  0x0,0x0,0x0,0x10,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/styles/dark_theme.qss
  0x0,0x0,0x0,0x22,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xbc,0x9d,0x26,0x74,
  // :/icons/play.svg
  0x0,0x0,0x0,0x44,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0x1f,
0x0,0x0,0x1,0x97,0xbc,0x9f,0xcd,0xb8,
  // :/icons/previous.svg
  0x0,0x0,0x0,0xe8,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xc,0x95,
0x0,0x0,0x1,0x97,0xbc,0xa0,0x34,0xc3,
  // :/icons/equalizer.svg
  0x0,0x0,0x0,0xc8,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xb,0xea,
0x0,0x0,0x1,0x97,0xbc,0xa1,0x8,0xe2,
  // :/icons/folder.svg
  0x0,0x0,0x0,0x5a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0xa7,
0x0,0x0,0x1,0x97,0xbc,0xa0,0xb4,0xfb,
  // :/icons/stop.svg
  0x0,0x0,0x1,0x6c,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x10,0xe1,
0x0,0x0,0x1,0x97,0xbc,0x9f,0xfd,0x54,
  // :/icons/repeat.svg
  0x0,0x0,0x1,0x6,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xd,0x2b,
0x0,0x0,0x1,0x97,0xbc,0xa0,0x73,0x81,
  // :/icons/settings.svg
  0x0,0x0,0x0,0xaa,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x8,0x13,
0x0,0x0,0x1,0x97,0xbc,0xa0,0xee,0x2,
  // :/icons/volume.svg
  0x0,0x0,0x1,0x20,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xd,0xe5,
0x0,0x0,0x1,0x97,0xbc,0xa0,0x93,0x55,
  // :/icons/pause.svg
  0x0,0x0,0x0,0x92,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0x79,
0x0,0x0,0x1,0x97,0xbc,0x9f,0xe6,0xf,
  // :/icons/next.svg
  0x0,0x0,0x1,0x3a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0x1b,
0x0,0x0,0x1,0x97,0xbc,0xa0,0x18,0x99,
  // :/icons/shuffle.svg
  0x0,0x0,0x1,0x50,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0xb9,
0x0,0x0,0x1,0x97,0xbc,0xa0,0x57,0xac,
  // :/icons/playlist.svg
  0x0,0x0,0x0,0x74,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0x7e,
0x0,0x0,0x1,0x97,0xbc,0xa1,0x27,0x23,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
