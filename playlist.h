#ifndef PLAYLIST_H
#define PLAYLIST_H

#include <QObject>
#include <QStringList>
#include <QFileInfo>
#include <QRandomGenerator>

struct Track {
    QString filePath;
    QString title;
    QString artist;
    QString album;
    qint64 duration;
    
    Track() : duration(0) {}
    Track(const QString &path) : filePath(path), duration(0) {
        QFileInfo info(path);
        title = info.baseName();
        artist = "Unknown Artist";
        album = "Unknown Album";
    }
};

class Playlist : public QObject
{
    Q_OBJECT

public:
    enum RepeatMode {
        NoRepeat,
        RepeatOne,
        RepeatAll
    };

    explicit Playlist(QObject *parent = nullptr);

    // Playlist management
    void addTrack(const QString &filePath);
    void addTracks(const QStringList &filePaths);
    void removeTrack(int index);
    void clearPlaylist();
    void moveTrack(int from, int to);

    // Playback control
    QString currentTrack() const;
    int currentIndex() const;
    void setCurrentIndex(int index);
    QString nextTrack();
    QString previousTrack();

    // Shuffle and repeat
    bool isShuffleEnabled() const { return m_shuffleEnabled; }
    void setShuffleEnabled(bool enabled);
    RepeatMode repeatMode() const { return m_repeatMode; }
    void setRepeatMode(RepeatMode mode);

    // Playlist info
    int count() const { return m_tracks.size(); }
    bool isEmpty() const { return m_tracks.isEmpty(); }
    Track trackAt(int index) const;
    QStringList trackTitles() const;

    // Playlist file operations
    void savePlaylist(const QString &filePath);
    void loadPlaylist(const QString &filePath);

signals:
    void playlistChanged();
    void currentTrackChanged(int index);
    void shuffleStateChanged(bool enabled);
    void repeatModeChanged(RepeatMode mode);

private:
    void generateShuffleOrder();
    int getNextShuffleIndex();
    int getPreviousShuffleIndex();

    QList<Track> m_tracks;
    int m_currentIndex;
    bool m_shuffleEnabled;
    RepeatMode m_repeatMode;
    QList<int> m_shuffleOrder;
    int m_shufflePosition;
};

#endif // PLAYLIST_H
