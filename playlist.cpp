#include "playlist.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QUrl>

Playlist::Playlist(QObject *parent)
    : QObject(parent)
    , m_currentIndex(-1)
    , m_shuffleEnabled(false)
    , m_repeatMode(NoRepeat)
    , m_shufflePosition(-1)
{
}

void Playlist::addTrack(const QString &filePath)
{
    Track track(filePath);
    m_tracks.append(track);
    
    if (m_currentIndex == -1) {
        m_currentIndex = 0;
    }
    
    if (m_shuffleEnabled) {
        generateShuffleOrder();
    }
    
    emit playlistChanged();
}

void Playlist::addTracks(const QStringList &filePaths)
{
    for (const QString &path : filePaths) {
        Track track(path);
        m_tracks.append(track);
    }
    
    if (m_currentIndex == -1 && !m_tracks.isEmpty()) {
        m_currentIndex = 0;
    }
    
    if (m_shuffleEnabled) {
        generateShuffleOrder();
    }
    
    emit playlistChanged();
}

void Playlist::removeTrack(int index)
{
    if (index < 0 || index >= m_tracks.size()) {
        return;
    }
    
    m_tracks.removeAt(index);
    
    if (index == m_currentIndex) {
        if (m_currentIndex >= m_tracks.size()) {
            m_currentIndex = m_tracks.size() - 1;
        }
    } else if (index < m_currentIndex) {
        m_currentIndex--;
    }
    
    if (m_tracks.isEmpty()) {
        m_currentIndex = -1;
    }
    
    if (m_shuffleEnabled) {
        generateShuffleOrder();
    }
    
    emit playlistChanged();
    emit currentTrackChanged(m_currentIndex);
}

void Playlist::clearPlaylist()
{
    m_tracks.clear();
    m_currentIndex = -1;
    m_shuffleOrder.clear();
    m_shufflePosition = -1;
    
    emit playlistChanged();
    emit currentTrackChanged(m_currentIndex);
}

void Playlist::moveTrack(int from, int to)
{
    if (from < 0 || from >= m_tracks.size() || to < 0 || to >= m_tracks.size()) {
        return;
    }
    
    Track track = m_tracks.takeAt(from);
    m_tracks.insert(to, track);
    
    // Update current index
    if (from == m_currentIndex) {
        m_currentIndex = to;
    } else if (from < m_currentIndex && to >= m_currentIndex) {
        m_currentIndex--;
    } else if (from > m_currentIndex && to <= m_currentIndex) {
        m_currentIndex++;
    }
    
    if (m_shuffleEnabled) {
        generateShuffleOrder();
    }
    
    emit playlistChanged();
    emit currentTrackChanged(m_currentIndex);
}

QString Playlist::currentTrack() const
{
    if (m_currentIndex >= 0 && m_currentIndex < m_tracks.size()) {
        return m_tracks[m_currentIndex].filePath;
    }
    return QString();
}

int Playlist::currentIndex() const
{
    return m_currentIndex;
}

void Playlist::setCurrentIndex(int index)
{
    if (index >= 0 && index < m_tracks.size()) {
        m_currentIndex = index;
        emit currentTrackChanged(m_currentIndex);
    }
}

QString Playlist::nextTrack()
{
    if (m_tracks.isEmpty()) {
        return QString();
    }
    
    if (m_repeatMode == RepeatOne) {
        // Stay on current track
        return currentTrack();
    }
    
    if (m_shuffleEnabled) {
        int nextIndex = getNextShuffleIndex();
        if (nextIndex != -1) {
            m_currentIndex = nextIndex;
            emit currentTrackChanged(m_currentIndex);
            return currentTrack();
        }
    } else {
        if (m_currentIndex < m_tracks.size() - 1) {
            m_currentIndex++;
            emit currentTrackChanged(m_currentIndex);
            return currentTrack();
        } else if (m_repeatMode == RepeatAll) {
            m_currentIndex = 0;
            emit currentTrackChanged(m_currentIndex);
            return currentTrack();
        }
    }
    
    return QString(); // End of playlist
}

QString Playlist::previousTrack()
{
    if (m_tracks.isEmpty()) {
        return QString();
    }
    
    if (m_repeatMode == RepeatOne) {
        // Stay on current track
        return currentTrack();
    }
    
    if (m_shuffleEnabled) {
        int prevIndex = getPreviousShuffleIndex();
        if (prevIndex != -1) {
            m_currentIndex = prevIndex;
            emit currentTrackChanged(m_currentIndex);
            return currentTrack();
        }
    } else {
        if (m_currentIndex > 0) {
            m_currentIndex--;
            emit currentTrackChanged(m_currentIndex);
            return currentTrack();
        } else if (m_repeatMode == RepeatAll) {
            m_currentIndex = m_tracks.size() - 1;
            emit currentTrackChanged(m_currentIndex);
            return currentTrack();
        }
    }
    
    return QString(); // Beginning of playlist
}

void Playlist::setShuffleEnabled(bool enabled)
{
    if (m_shuffleEnabled != enabled) {
        m_shuffleEnabled = enabled;
        
        if (enabled) {
            generateShuffleOrder();
        } else {
            m_shuffleOrder.clear();
            m_shufflePosition = -1;
        }
        
        emit shuffleStateChanged(enabled);
    }
}

void Playlist::setRepeatMode(RepeatMode mode)
{
    if (m_repeatMode != mode) {
        m_repeatMode = mode;
        emit repeatModeChanged(mode);
    }
}

Track Playlist::trackAt(int index) const
{
    if (index >= 0 && index < m_tracks.size()) {
        return m_tracks[index];
    }
    return Track();
}

QStringList Playlist::trackTitles() const
{
    QStringList titles;
    for (const Track &track : m_tracks) {
        titles.append(track.title);
    }
    return titles;
}

void Playlist::savePlaylist(const QString &filePath)
{
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << "#EXTM3U\n";
        
        for (const Track &track : m_tracks) {
            out << "#EXTINF:" << (track.duration / 1000) << "," 
                << track.artist << " - " << track.title << "\n";
            out << track.filePath << "\n";
        }
    }
}

void Playlist::loadPlaylist(const QString &filePath)
{
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        clearPlaylist();
        
        while (!in.atEnd()) {
            QString line = in.readLine().trimmed();
            if (!line.startsWith("#") && !line.isEmpty()) {
                // Check if it's a relative path
                QFileInfo info(line);
                if (info.isRelative()) {
                    QDir playlistDir(QFileInfo(filePath).absolutePath());
                    line = playlistDir.absoluteFilePath(line);
                }
                
                if (QFileInfo::exists(line)) {
                    addTrack(line);
                }
            }
        }
    }
}

void Playlist::generateShuffleOrder()
{
    m_shuffleOrder.clear();
    
    if (m_tracks.isEmpty()) {
        m_shufflePosition = -1;
        return;
    }
    
    // Create list of indices
    for (int i = 0; i < m_tracks.size(); ++i) {
        m_shuffleOrder.append(i);
    }
    
    // Shuffle the order
    for (int i = m_shuffleOrder.size() - 1; i > 0; --i) {
        int j = QRandomGenerator::global()->bounded(i + 1);
        m_shuffleOrder.swapItemsAt(i, j);
    }
    
    // Find current position in shuffle order
    m_shufflePosition = m_shuffleOrder.indexOf(m_currentIndex);
}

int Playlist::getNextShuffleIndex()
{
    if (m_shuffleOrder.isEmpty()) {
        return -1;
    }
    
    m_shufflePosition++;
    
    if (m_shufflePosition >= m_shuffleOrder.size()) {
        if (m_repeatMode == RepeatAll) {
            m_shufflePosition = 0;
        } else {
            return -1; // End of shuffle
        }
    }
    
    return m_shuffleOrder[m_shufflePosition];
}

int Playlist::getPreviousShuffleIndex()
{
    if (m_shuffleOrder.isEmpty()) {
        return -1;
    }
    
    m_shufflePosition--;
    
    if (m_shufflePosition < 0) {
        if (m_repeatMode == RepeatAll) {
            m_shufflePosition = m_shuffleOrder.size() - 1;
        } else {
            return -1; // Beginning of shuffle
        }
    }
    
    return m_shuffleOrder[m_shufflePosition];
}
