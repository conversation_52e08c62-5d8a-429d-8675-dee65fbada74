#ifndef AUDIOPLAYER_H
#define AUDIOPLAYER_H

#include <QObject>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QUrl>

class AudioPlayer : public QObject
{
    Q_OBJECT

public:
    explicit AudioPlayer(QObject *parent = nullptr);
    ~AudioPlayer();

    void setSource(const QString &filePath);
    void play();
    void pause();
    void stop();
    void setPosition(qint64 position);
    void setVolume(int volume);
    
    bool isPlaying() const;
    qint64 position() const;
    qint64 duration() const;

signals:
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);
    void playbackStateChanged();

private slots:
    void handlePositionChanged(qint64 position);
    void handleDurationChanged(qint64 duration);
    void handlePlaybackStateChanged(QMediaPlayer::PlaybackState state);

private:
    QMediaPlayer *m_mediaPlayer;
    QAudioOutput *m_audioOutput;
};

#endif // AUDIOPLAYER_H
