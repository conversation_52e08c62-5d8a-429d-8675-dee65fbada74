#include "mainwindow.h"
#include "settingsdialog.h"
#include <QApplication>
#include <QFileInfo>
#include <QDir>
#include <QCloseEvent>
#include <QStandardPaths>
#include <QSplitter>
#include <QGroupBox>
#include <QListWidgetItem>
#include <QMenu>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_audioPlayer(new AudioPlayer(this))
    , m_playlist(new Playlist(this))
    , m_equalizer(nullptr)
    , m_duration(0)
    , m_playlistVisible(true)
{
    loadStyleSheet();
    setupUI();
    setupMenuBar();
    setupStatusBar();
    setupSystemTray();
    setupShortcuts();

    // Connect audio player signals
    connect(m_audioPlayer, &AudioPlayer::positionChanged, this, &MainWindow::updatePosition);
    connect(m_audioPlayer, &AudioPlayer::durationChanged, this, &MainWindow::updateDuration);
    connect(m_audioPlayer, &AudioPlayer::playbackStateChanged, this, &MainWindow::updatePlaybackState);

    // Connect playlist signals
    connect(m_playlist, &Playlist::playlistChanged, this, &MainWindow::onPlaylistChanged);
    connect(m_playlist, &Playlist::currentTrackChanged, this, &MainWindow::onCurrentTrackChanged);

    // Set initial values
    m_audioPlayer->setVolume(50);
    m_volumeSlider->setValue(50);
    m_speedSpinBox->setValue(100);

    updateWindowTitle();
}

MainWindow::~MainWindow()
{
    if (m_equalizer) {
        delete m_equalizer;
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (m_systemTray && m_systemTray->isVisible()) {
        hide();
        event->ignore();
    } else {
        event->accept();
    }
}

void MainWindow::loadStyleSheet()
{
    QFile file(":/styles/dark_theme.qss");
    if (file.open(QFile::ReadOnly)) {
        QString styleSheet = QLatin1String(file.readAll());
        qApp->setStyleSheet(styleSheet);
    }
}

void MainWindow::setupUI()
{
    setWindowTitle("Modern Audio Player");
    setMinimumSize(800, 600);
    resize(1000, 700);

    // Create central widget with splitter
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    QHBoxLayout *mainLayout = new QHBoxLayout(centralWidget);
    mainLayout->setContentsMargins(10, 10, 10, 10);

    // Main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(m_mainSplitter);

    // Left panel - Player controls
    QWidget *leftPanel = new QWidget(this);
    leftPanel->setMinimumWidth(400);
    QVBoxLayout *leftLayout = new QVBoxLayout(leftPanel);

    // Track info section
    QGroupBox *trackInfoGroup = new QGroupBox("Now Playing", this);
    QVBoxLayout *trackInfoLayout = new QVBoxLayout(trackInfoGroup);

    m_fileNameLabel = new QLabel("No track selected", this);
    m_fileNameLabel->setObjectName("fileNameLabel");
    m_fileNameLabel->setAlignment(Qt::AlignCenter);
    m_fileNameLabel->setWordWrap(true);
    trackInfoLayout->addWidget(m_fileNameLabel);

    m_artistLabel = new QLabel("Unknown Artist", this);
    m_artistLabel->setAlignment(Qt::AlignCenter);
    m_artistLabel->setStyleSheet("color: #cccccc; font-size: 10pt;");
    trackInfoLayout->addWidget(m_artistLabel);

    m_albumLabel = new QLabel("Unknown Album", this);
    m_albumLabel->setAlignment(Qt::AlignCenter);
    m_albumLabel->setStyleSheet("color: #aaaaaa; font-size: 9pt;");
    trackInfoLayout->addWidget(m_albumLabel);

    leftLayout->addWidget(trackInfoGroup);

    // Position control
    QHBoxLayout *positionLayout = new QHBoxLayout();
    m_currentTimeLabel = new QLabel("00:00", this);
    m_currentTimeLabel->setObjectName("currentTimeLabel");
    m_positionSlider = new QSlider(Qt::Horizontal, this);
    m_totalTimeLabel = new QLabel("00:00", this);
    m_totalTimeLabel->setObjectName("totalTimeLabel");

    positionLayout->addWidget(m_currentTimeLabel);
    positionLayout->addWidget(m_positionSlider);
    positionLayout->addWidget(m_totalTimeLabel);
    leftLayout->addLayout(positionLayout);

    // Main control buttons
    QHBoxLayout *mainControlLayout = new QHBoxLayout();
    mainControlLayout->setSpacing(15);
    mainControlLayout->addStretch();

    m_shuffleButton = new QPushButton(this);
    m_shuffleButton->setIcon(QIcon(":/icons/shuffle.svg"));
    m_shuffleButton->setCheckable(true);
    m_shuffleButton->setToolTip("Shuffle");
    m_shuffleButton->setFixedSize(35, 35);
    mainControlLayout->addWidget(m_shuffleButton);

    m_previousButton = new QPushButton(this);
    m_previousButton->setObjectName("previousButton");
    m_previousButton->setIcon(QIcon(":/icons/previous.svg"));
    m_previousButton->setToolTip("Previous Track");
    mainControlLayout->addWidget(m_previousButton);

    m_playPauseButton = new QPushButton(this);
    m_playPauseButton->setObjectName("playPauseButton");
    m_playPauseButton->setIcon(QIcon(":/icons/play.svg"));
    m_playPauseButton->setToolTip("Play/Pause");
    m_playPauseButton->setEnabled(false);
    mainControlLayout->addWidget(m_playPauseButton);

    m_stopButton = new QPushButton(this);
    m_stopButton->setObjectName("stopButton");
    m_stopButton->setIcon(QIcon(":/icons/stop.svg"));
    m_stopButton->setToolTip("Stop");
    m_stopButton->setEnabled(false);
    mainControlLayout->addWidget(m_stopButton);

    m_nextButton = new QPushButton(this);
    m_nextButton->setObjectName("nextButton");
    m_nextButton->setIcon(QIcon(":/icons/next.svg"));
    m_nextButton->setToolTip("Next Track");
    mainControlLayout->addWidget(m_nextButton);

    m_repeatButton = new QPushButton(this);
    m_repeatButton->setIcon(QIcon(":/icons/repeat.svg"));
    m_repeatButton->setCheckable(true);
    m_repeatButton->setToolTip("Repeat");
    m_repeatButton->setFixedSize(35, 35);
    mainControlLayout->addWidget(m_repeatButton);

    mainControlLayout->addStretch();
    leftLayout->addLayout(mainControlLayout);

    // Secondary controls
    QHBoxLayout *secondaryLayout = new QHBoxLayout();

    // File operations
    m_openButton = new QPushButton(this);
    m_openButton->setObjectName("openButton");
    m_openButton->setIcon(QIcon(":/icons/folder.svg"));
    m_openButton->setText("Open");
    m_openButton->setToolTip("Open Audio File(s)");
    secondaryLayout->addWidget(m_openButton);

    secondaryLayout->addStretch();

    // Speed control
    m_speedLabel = new QLabel("Speed:", this);
    m_speedSpinBox = new QSpinBox(this);
    m_speedSpinBox->setRange(25, 300);
    m_speedSpinBox->setValue(100);
    m_speedSpinBox->setSuffix("%");
    m_speedSpinBox->setToolTip("Playback Speed");
    secondaryLayout->addWidget(m_speedLabel);
    secondaryLayout->addWidget(m_speedSpinBox);

    secondaryLayout->addStretch();

    // Volume control
    QLabel *volumeIcon = new QLabel(this);
    volumeIcon->setPixmap(QIcon(":/icons/volume.svg").pixmap(16, 16));
    m_volumeSlider = new QSlider(Qt::Horizontal, this);
    m_volumeSlider->setObjectName("volumeSlider");
    m_volumeSlider->setRange(0, 100);
    m_volumeSlider->setValue(50);
    m_volumeSlider->setMaximumWidth(120);
    m_volumeSlider->setToolTip("Volume");

    secondaryLayout->addWidget(volumeIcon);
    secondaryLayout->addWidget(m_volumeSlider);

    leftLayout->addLayout(secondaryLayout);
    leftLayout->addStretch();

    m_mainSplitter->addWidget(leftPanel);

    // Right panel - Playlist and controls
    m_rightPanel = new QTabWidget(this);

    // Playlist tab
    QWidget *playlistTab = new QWidget(this);
    QVBoxLayout *playlistLayout = new QVBoxLayout(playlistTab);

    // Playlist controls
    QHBoxLayout *playlistControlLayout = new QHBoxLayout();

    QPushButton *addFilesButton = new QPushButton("Add Files", this);
    addFilesButton->setIcon(QIcon(":/icons/folder.svg"));
    QPushButton *addFolderButton = new QPushButton("Add Folder", this);
    QPushButton *clearPlaylistButton = new QPushButton("Clear", this);
    QPushButton *savePlaylistButton = new QPushButton("Save", this);
    QPushButton *loadPlaylistButton = new QPushButton("Load", this);

    playlistControlLayout->addWidget(addFilesButton);
    playlistControlLayout->addWidget(addFolderButton);
    playlistControlLayout->addStretch();
    playlistControlLayout->addWidget(savePlaylistButton);
    playlistControlLayout->addWidget(loadPlaylistButton);
    playlistControlLayout->addWidget(clearPlaylistButton);

    playlistLayout->addLayout(playlistControlLayout);

    // Playlist widget
    m_playlistWidget = new QListWidget(this);
    m_playlistWidget->setAlternatingRowColors(true);
    m_playlistWidget->setSelectionMode(QAbstractItemView::ExtendedSelection);
    m_playlistWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    playlistLayout->addWidget(m_playlistWidget);

    m_rightPanel->addTab(playlistTab, QIcon(":/icons/playlist.svg"), "Playlist");

    // Equalizer tab placeholder
    QWidget *equalizerTab = new QWidget(this);
    QVBoxLayout *eqLayout = new QVBoxLayout(equalizerTab);

    m_equalizerButton = new QPushButton("Open Equalizer", this);
    m_equalizerButton->setIcon(QIcon(":/icons/equalizer.svg"));
    eqLayout->addWidget(m_equalizerButton);
    eqLayout->addStretch();

    m_rightPanel->addTab(equalizerTab, QIcon(":/icons/equalizer.svg"), "Audio");

    m_mainSplitter->addWidget(m_rightPanel);
    m_mainSplitter->setSizes({600, 400});

    // Connect all signals
    connect(m_openButton, &QPushButton::clicked, this, &MainWindow::openFiles);
    connect(m_playPauseButton, &QPushButton::clicked, this, &MainWindow::playPause);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::stop);
    connect(m_nextButton, &QPushButton::clicked, this, &MainWindow::nextTrack);
    connect(m_previousButton, &QPushButton::clicked, this, &MainWindow::previousTrack);
    connect(m_shuffleButton, &QPushButton::toggled, this, &MainWindow::shuffleToggle);
    connect(m_repeatButton, &QPushButton::clicked, this, &MainWindow::repeatToggle);
    connect(m_equalizerButton, &QPushButton::clicked, this, &MainWindow::showEqualizer);

    connect(m_positionSlider, &QSlider::sliderMoved, this, &MainWindow::setPosition);
    connect(m_volumeSlider, &QSlider::valueChanged, this, &MainWindow::setVolume);
    connect(m_speedSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, [this](int value) { setSpeed(value / 100.0); });

    connect(addFilesButton, &QPushButton::clicked, this, &MainWindow::openFiles);
    connect(addFolderButton, &QPushButton::clicked, this, &MainWindow::openFolder);
    connect(clearPlaylistButton, &QPushButton::clicked, this, &MainWindow::clearPlaylist);
    connect(savePlaylistButton, &QPushButton::clicked, this, &MainWindow::savePlaylist);
    connect(loadPlaylistButton, &QPushButton::clicked, this, &MainWindow::loadPlaylist);

    connect(m_playlistWidget, &QListWidget::itemDoubleClicked,
            this, &MainWindow::playlistItemDoubleClicked);
    connect(m_playlistWidget, &QListWidget::customContextMenuRequested,
            this, [this](const QPoint &pos) {
                QMenu menu(this);
                menu.addAction("Remove from playlist", this, &MainWindow::removeFromPlaylist);
                menu.exec(m_playlistWidget->mapToGlobal(pos));
            });
}

void MainWindow::setupMenuBar()
{
    QMenuBar *menuBar = this->menuBar();

    // File menu
    QMenu *fileMenu = menuBar->addMenu("&File");

    QAction *openFileAction = fileMenu->addAction(QIcon(":/icons/folder.svg"), "&Open File(s)...");
    openFileAction->setShortcut(QKeySequence::Open);
    connect(openFileAction, &QAction::triggered, this, &MainWindow::openFiles);

    QAction *openFolderAction = fileMenu->addAction("Open &Folder...");
    openFolderAction->setShortcut(QKeySequence("Ctrl+Shift+O"));
    connect(openFolderAction, &QAction::triggered, this, &MainWindow::openFolder);

    fileMenu->addSeparator();

    QAction *savePlaylistAction = fileMenu->addAction("&Save Playlist...");
    savePlaylistAction->setShortcut(QKeySequence::Save);
    connect(savePlaylistAction, &QAction::triggered, this, &MainWindow::savePlaylist);

    QAction *loadPlaylistAction = fileMenu->addAction("&Load Playlist...");
    connect(loadPlaylistAction, &QAction::triggered, this, &MainWindow::loadPlaylist);

    fileMenu->addSeparator();

    QAction *exitAction = fileMenu->addAction("E&xit");
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // Playback menu
    QMenu *playbackMenu = menuBar->addMenu("&Playback");

    m_playAction = playbackMenu->addAction(QIcon(":/icons/play.svg"), "&Play/Pause");
    m_playAction->setShortcut(QKeySequence("Space"));
    connect(m_playAction, &QAction::triggered, this, &MainWindow::playPause);

    m_stopAction = playbackMenu->addAction(QIcon(":/icons/stop.svg"), "&Stop");
    m_stopAction->setShortcut(QKeySequence("Ctrl+."));
    connect(m_stopAction, &QAction::triggered, this, &MainWindow::stop);

    playbackMenu->addSeparator();

    m_nextAction = playbackMenu->addAction(QIcon(":/icons/next.svg"), "&Next Track");
    m_nextAction->setShortcut(QKeySequence("Ctrl+Right"));
    connect(m_nextAction, &QAction::triggered, this, &MainWindow::nextTrack);

    m_previousAction = playbackMenu->addAction(QIcon(":/icons/previous.svg"), "&Previous Track");
    m_previousAction->setShortcut(QKeySequence("Ctrl+Left"));
    connect(m_previousAction, &QAction::triggered, this, &MainWindow::previousTrack);

    playbackMenu->addSeparator();

    m_shuffleAction = playbackMenu->addAction(QIcon(":/icons/shuffle.svg"), "S&huffle");
    m_shuffleAction->setCheckable(true);
    m_shuffleAction->setShortcut(QKeySequence("Ctrl+H"));
    connect(m_shuffleAction, &QAction::triggered, this, &MainWindow::shuffleToggle);

    m_repeatAction = playbackMenu->addAction(QIcon(":/icons/repeat.svg"), "&Repeat");
    m_repeatAction->setCheckable(true);
    m_repeatAction->setShortcut(QKeySequence("Ctrl+R"));
    connect(m_repeatAction, &QAction::triggered, this, &MainWindow::repeatToggle);

    // Tools menu
    QMenu *toolsMenu = menuBar->addMenu("&Tools");

    QAction *equalizerAction = toolsMenu->addAction(QIcon(":/icons/equalizer.svg"), "&Equalizer...");
    equalizerAction->setShortcut(QKeySequence("Ctrl+E"));
    connect(equalizerAction, &QAction::triggered, this, &MainWindow::showEqualizer);

    toolsMenu->addSeparator();

    QAction *settingsAction = toolsMenu->addAction(QIcon(":/icons/settings.svg"), "&Settings...");
    settingsAction->setShortcut(QKeySequence::Preferences);
    connect(settingsAction, &QAction::triggered, this, &MainWindow::showSettings);

    // View menu
    QMenu *viewMenu = menuBar->addMenu("&View");

    QAction *togglePlaylistAction = viewMenu->addAction("Toggle &Playlist");
    togglePlaylistAction->setShortcut(QKeySequence("F9"));
    connect(togglePlaylistAction, &QAction::triggered, this, &MainWindow::togglePlaylistView);
}

void MainWindow::setupStatusBar()
{
    QStatusBar *statusBar = this->statusBar();

    m_statusLabel = new QLabel("Ready", this);
    statusBar->addWidget(m_statusLabel);

    m_loadingProgress = new QProgressBar(this);
    m_loadingProgress->setVisible(false);
    m_loadingProgress->setMaximumWidth(200);
    statusBar->addPermanentWidget(m_loadingProgress);
}

void MainWindow::setupSystemTray()
{
    if (QSystemTrayIcon::isSystemTrayAvailable()) {
        m_systemTray = new QSystemTrayIcon(QIcon(":/icons/play.svg"), this);

        m_trayMenu = new QMenu(this);
        m_trayMenu->addAction("Show", this, &QWidget::showNormal);
        m_trayMenu->addAction("Hide", this, &QWidget::hide);
        m_trayMenu->addSeparator();
        m_trayMenu->addAction("Play/Pause", this, &MainWindow::playPause);
        m_trayMenu->addAction("Stop", this, &MainWindow::stop);
        m_trayMenu->addAction("Next", this, &MainWindow::nextTrack);
        m_trayMenu->addAction("Previous", this, &MainWindow::previousTrack);
        m_trayMenu->addSeparator();
        m_trayMenu->addAction("Exit", this, &QWidget::close);

        m_systemTray->setContextMenu(m_trayMenu);
        connect(m_systemTray, &QSystemTrayIcon::activated,
                this, &MainWindow::onSystemTrayActivated);

        m_systemTray->show();
    }
}

void MainWindow::setupShortcuts()
{
    // Global shortcuts for media keys would go here
    // Note: This requires additional platform-specific code
}

// File operations
void MainWindow::openFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Audio File", "", "Audio Files (*.mp3 *.wav *.ogg *.flac *.m4a *.aac)");

    if (!fileName.isEmpty()) {
        m_playlist->clearPlaylist();
        m_playlist->addTrack(fileName);
        QString currentTrack = m_playlist->currentTrack();
        if (!currentTrack.isEmpty()) {
            m_audioPlayer->setSource(currentTrack);
            updateWindowTitle();
            updatePlaylistDisplay();
        }
    }
}

void MainWindow::openFiles()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(this,
        "Open Audio Files", "", "Audio Files (*.mp3 *.wav *.ogg *.flac *.m4a *.aac)");

    if (!fileNames.isEmpty()) {
        m_playlist->addTracks(fileNames);
        if (!m_playlist->currentTrack().isEmpty()) {
            m_audioPlayer->setSource(m_playlist->currentTrack());
            updateWindowTitle();
            updatePlaylistDisplay();
        }
    }
}

void MainWindow::openFolder()
{
    QString folderPath = QFileDialog::getExistingDirectory(this, "Open Folder");

    if (!folderPath.isEmpty()) {
        QDir dir(folderPath);
        QStringList filters;
        filters << "*.mp3" << "*.wav" << "*.ogg" << "*.flac" << "*.m4a" << "*.aac";

        QStringList files = dir.entryList(filters, QDir::Files);
        QStringList fullPaths;

        for (const QString &file : files) {
            fullPaths.append(dir.absoluteFilePath(file));
        }

        if (!fullPaths.isEmpty()) {
            m_playlist->addTracks(fullPaths);
            if (!m_playlist->currentTrack().isEmpty()) {
                m_audioPlayer->setSource(m_playlist->currentTrack());
                updateWindowTitle();
                updatePlaylistDisplay();
            }
        }
    }
}

void MainWindow::savePlaylist()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Playlist", "", "Playlist Files (*.m3u)");

    if (!fileName.isEmpty()) {
        m_playlist->savePlaylist(fileName);
        m_statusLabel->setText("Playlist saved");
    }
}

void MainWindow::loadPlaylist()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Load Playlist", "", "Playlist Files (*.m3u)");

    if (!fileName.isEmpty()) {
        m_playlist->loadPlaylist(fileName);
        updatePlaylistDisplay();
        if (!m_playlist->currentTrack().isEmpty()) {
            m_audioPlayer->setSource(m_playlist->currentTrack());
            updateWindowTitle();
        }
        m_statusLabel->setText("Playlist loaded");
    }
}

// Playback control
void MainWindow::playPause()
{
    if (m_playlist->isEmpty()) {
        openFiles();
        return;
    }

    if (m_audioPlayer->isPlaying()) {
        m_audioPlayer->pause();
    } else {
        m_audioPlayer->play();
    }
}

void MainWindow::stop()
{
    m_audioPlayer->stop();
}

void MainWindow::nextTrack()
{
    QString nextTrack = m_playlist->nextTrack();
    if (!nextTrack.isEmpty()) {
        m_audioPlayer->setSource(nextTrack);
        if (m_audioPlayer->isPlaying()) {
            m_audioPlayer->play();
        }
        updateWindowTitle();
        updatePlaylistDisplay();
    }
}

void MainWindow::previousTrack()
{
    QString prevTrack = m_playlist->previousTrack();
    if (!prevTrack.isEmpty()) {
        m_audioPlayer->setSource(prevTrack);
        if (m_audioPlayer->isPlaying()) {
            m_audioPlayer->play();
        }
        updateWindowTitle();
        updatePlaylistDisplay();
    }
}

void MainWindow::setPosition(int position)
{
    m_audioPlayer->setPosition(position);
}

void MainWindow::setVolume(int volume)
{
    m_audioPlayer->setVolume(volume);
}

void MainWindow::setSpeed(double speed)
{
    // Speed control would be implemented here
    // Note: QMediaPlayer doesn't directly support speed control
    // This would require additional audio processing
}

// Playlist operations
void MainWindow::shuffleToggle()
{
    bool enabled = m_shuffleButton->isChecked();
    m_playlist->setShuffleEnabled(enabled);
    m_shuffleAction->setChecked(enabled);
}

void MainWindow::repeatToggle()
{
    Playlist::RepeatMode currentMode = m_playlist->repeatMode();
    Playlist::RepeatMode newMode;

    switch (currentMode) {
        case Playlist::NoRepeat:
            newMode = Playlist::RepeatAll;
            m_repeatButton->setStyleSheet("background-color: #0078d4;");
            break;
        case Playlist::RepeatAll:
            newMode = Playlist::RepeatOne;
            m_repeatButton->setStyleSheet("background-color: #d83b01;");
            break;
        case Playlist::RepeatOne:
            newMode = Playlist::NoRepeat;
            m_repeatButton->setStyleSheet("");
            break;
    }

    m_playlist->setRepeatMode(newMode);
    m_repeatAction->setChecked(newMode != Playlist::NoRepeat);
}

void MainWindow::removeFromPlaylist()
{
    int currentRow = m_playlistWidget->currentRow();
    if (currentRow >= 0) {
        m_playlist->removeTrack(currentRow);
    }
}

void MainWindow::clearPlaylist()
{
    m_playlist->clearPlaylist();
    m_audioPlayer->stop();
    updateWindowTitle();
}

void MainWindow::playlistItemDoubleClicked(QListWidgetItem *item)
{
    int index = m_playlistWidget->row(item);
    m_playlist->setCurrentIndex(index);
    m_audioPlayer->setSource(m_playlist->currentTrack());
    m_audioPlayer->play();
    updateWindowTitle();
    updatePlaylistDisplay();
}

void MainWindow::onPlaylistChanged()
{
    updatePlaylistDisplay();

    // Enable/disable controls based on playlist state
    bool hasItems = !m_playlist->isEmpty();
    m_playPauseButton->setEnabled(hasItems);
    m_stopButton->setEnabled(hasItems);
    m_nextButton->setEnabled(hasItems);
    m_previousButton->setEnabled(hasItems);
}

void MainWindow::onCurrentTrackChanged(int index)
{
    updatePlaylistDisplay();
    updateWindowTitle();

    // Highlight current track in playlist
    if (index >= 0 && index < m_playlistWidget->count()) {
        m_playlistWidget->setCurrentRow(index);
    }
}

// Audio updates
void MainWindow::updatePosition(qint64 position)
{
    m_positionSlider->setValue(position);
    m_currentTimeLabel->setText(formatTime(position));
}

void MainWindow::updateDuration(qint64 duration)
{
    m_duration = duration;
    m_positionSlider->setRange(0, duration);
    m_totalTimeLabel->setText(formatTime(duration));
}

void MainWindow::updatePlaybackState()
{
    if (m_audioPlayer->isPlaying()) {
        m_playPauseButton->setIcon(QIcon(":/icons/pause.svg"));
        m_playPauseButton->setToolTip("Pause");
        m_playAction->setText("Pause");
        m_playAction->setIcon(QIcon(":/icons/pause.svg"));
        m_statusLabel->setText("Playing");
    } else {
        m_playPauseButton->setIcon(QIcon(":/icons/play.svg"));
        m_playPauseButton->setToolTip("Play");
        m_playAction->setText("Play");
        m_playAction->setIcon(QIcon(":/icons/play.svg"));
        m_statusLabel->setText("Paused");
    }
}

// UI and settings
void MainWindow::showEqualizer()
{
    if (!m_equalizer) {
        m_equalizer = new Equalizer(this);
    }
    m_equalizer->show();
    m_equalizer->raise();
    m_equalizer->activateWindow();
}

void MainWindow::showSettings()
{
    SettingsDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        // Apply settings changes
        m_statusLabel->setText("Settings updated");
    }
}

void MainWindow::togglePlaylistView()
{
    m_playlistVisible = !m_playlistVisible;
    m_rightPanel->setVisible(m_playlistVisible);
}

void MainWindow::onSystemTrayActivated(QSystemTrayIcon::ActivationReason reason)
{
    if (reason == QSystemTrayIcon::DoubleClick) {
        if (isVisible()) {
            hide();
        } else {
            showNormal();
            activateWindow();
            raise();
        }
    }
}

// Helper methods
QString MainWindow::formatTime(qint64 timeInMs)
{
    qint64 seconds = timeInMs / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}

void MainWindow::updatePlaylistDisplay()
{
    m_playlistWidget->clear();

    QStringList trackTitles = m_playlist->trackTitles();
    for (int i = 0; i < trackTitles.size(); ++i) {
        QListWidgetItem *item = new QListWidgetItem(trackTitles[i]);

        // Highlight current track
        if (i == m_playlist->currentIndex()) {
            item->setBackground(QColor("#0078d4"));
            item->setForeground(QColor("#ffffff"));
        }

        m_playlistWidget->addItem(item);
    }
}

void MainWindow::updateWindowTitle()
{
    QString title = "Modern Audio Player";

    if (!m_playlist->isEmpty()) {
        Track currentTrack = m_playlist->trackAt(m_playlist->currentIndex());
        if (!currentTrack.title.isEmpty()) {
            title = QString("%1 - %2").arg(currentTrack.title, title);
            m_fileNameLabel->setText(currentTrack.title);
            m_artistLabel->setText(currentTrack.artist);
            m_albumLabel->setText(currentTrack.album);
        }
    } else {
        m_fileNameLabel->setText("No track selected");
        m_artistLabel->setText("Unknown Artist");
        m_albumLabel->setText("Unknown Album");
    }

    setWindowTitle(title);
}

void MainWindow::openFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Audio File", "", "Audio Files (*.mp3 *.wav *.ogg *.flac *.m4a)");
    
    if (!fileName.isEmpty()) {
        m_audioPlayer->setSource(fileName);
        QFileInfo fileInfo(fileName);
        m_fileNameLabel->setText(fileInfo.baseName());
        m_playPauseButton->setEnabled(true);
        m_stopButton->setEnabled(true);
    }
}

void MainWindow::playPause()
{
    if (m_audioPlayer->isPlaying()) {
        m_audioPlayer->pause();
    } else {
        m_audioPlayer->play();
    }
}

void MainWindow::stop()
{
    m_audioPlayer->stop();
}

void MainWindow::setPosition(int position)
{
    m_audioPlayer->setPosition(position);
}

void MainWindow::setVolume(int volume)
{
    m_audioPlayer->setVolume(volume);
}

void MainWindow::updatePosition(qint64 position)
{
    m_positionSlider->setValue(position);
    m_currentTimeLabel->setText(formatTime(position));
}

void MainWindow::updateDuration(qint64 duration)
{
    m_duration = duration;
    m_positionSlider->setRange(0, duration);
    m_totalTimeLabel->setText(formatTime(duration));
}

void MainWindow::updatePlaybackState()
{
    if (m_audioPlayer->isPlaying()) {
        m_playPauseButton->setText("Pause");
    } else {
        m_playPauseButton->setText("Play");
    }
}

QString MainWindow::formatTime(qint64 timeInMs)
{
    qint64 seconds = timeInMs / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;
    
    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}
