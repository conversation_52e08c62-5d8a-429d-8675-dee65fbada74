#include "mainwindow.h"
#include <QApplication>
#include <QFileInfo>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_audioPlayer(new AudioPlayer(this))
    , m_duration(0)
{
    setupUI();
    
    // Connect audio player signals
    connect(m_audioPlayer, &AudioPlayer::positionChanged, this, &MainWindow::updatePosition);
    connect(m_audioPlayer, &AudioPlayer::durationChanged, this, &MainWindow::updateDuration);
    connect(m_audioPlayer, &AudioPlayer::playbackStateChanged, this, &MainWindow::updatePlaybackState);
    
    // Set initial volume
    m_audioPlayer->setVolume(50);
    m_volumeSlider->setValue(50);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    setWindowTitle("Audio Player");
    setMinimumSize(400, 200);
    
    // Create central widget and main layout
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    
    // File name label
    m_fileNameLabel = new QLabel("No file selected", this);
    m_fileNameLabel->setAlignment(Qt::AlignCenter);
    m_fileNameLabel->setStyleSheet("font-weight: bold; padding: 10px;");
    mainLayout->addWidget(m_fileNameLabel);
    
    // Position slider and time labels
    QHBoxLayout *positionLayout = new QHBoxLayout();
    m_currentTimeLabel = new QLabel("00:00", this);
    m_positionSlider = new QSlider(Qt::Horizontal, this);
    m_totalTimeLabel = new QLabel("00:00", this);
    
    positionLayout->addWidget(m_currentTimeLabel);
    positionLayout->addWidget(m_positionSlider);
    positionLayout->addWidget(m_totalTimeLabel);
    mainLayout->addLayout(positionLayout);
    
    // Control buttons
    QHBoxLayout *controlLayout = new QHBoxLayout();
    m_openButton = new QPushButton("Open File", this);
    m_playPauseButton = new QPushButton("Play", this);
    m_stopButton = new QPushButton("Stop", this);
    
    m_playPauseButton->setEnabled(false);
    m_stopButton->setEnabled(false);
    
    controlLayout->addWidget(m_openButton);
    controlLayout->addWidget(m_playPauseButton);
    controlLayout->addWidget(m_stopButton);
    mainLayout->addLayout(controlLayout);
    
    // Volume control
    QHBoxLayout *volumeLayout = new QHBoxLayout();
    m_volumeLabel = new QLabel("Volume:", this);
    m_volumeSlider = new QSlider(Qt::Horizontal, this);
    m_volumeSlider->setRange(0, 100);
    m_volumeSlider->setValue(50);
    m_volumeSlider->setMaximumWidth(150);
    
    volumeLayout->addWidget(m_volumeLabel);
    volumeLayout->addWidget(m_volumeSlider);
    volumeLayout->addStretch();
    mainLayout->addLayout(volumeLayout);
    
    // Connect button signals
    connect(m_openButton, &QPushButton::clicked, this, &MainWindow::openFile);
    connect(m_playPauseButton, &QPushButton::clicked, this, &MainWindow::playPause);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::stop);
    connect(m_positionSlider, &QSlider::sliderMoved, this, &MainWindow::setPosition);
    connect(m_volumeSlider, &QSlider::valueChanged, this, &MainWindow::setVolume);
}

void MainWindow::openFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Audio File", "", "Audio Files (*.mp3 *.wav *.ogg *.flac *.m4a)");
    
    if (!fileName.isEmpty()) {
        m_audioPlayer->setSource(fileName);
        QFileInfo fileInfo(fileName);
        m_fileNameLabel->setText(fileInfo.baseName());
        m_playPauseButton->setEnabled(true);
        m_stopButton->setEnabled(true);
    }
}

void MainWindow::playPause()
{
    if (m_audioPlayer->isPlaying()) {
        m_audioPlayer->pause();
    } else {
        m_audioPlayer->play();
    }
}

void MainWindow::stop()
{
    m_audioPlayer->stop();
}

void MainWindow::setPosition(int position)
{
    m_audioPlayer->setPosition(position);
}

void MainWindow::setVolume(int volume)
{
    m_audioPlayer->setVolume(volume);
}

void MainWindow::updatePosition(qint64 position)
{
    m_positionSlider->setValue(position);
    m_currentTimeLabel->setText(formatTime(position));
}

void MainWindow::updateDuration(qint64 duration)
{
    m_duration = duration;
    m_positionSlider->setRange(0, duration);
    m_totalTimeLabel->setText(formatTime(duration));
}

void MainWindow::updatePlaybackState()
{
    if (m_audioPlayer->isPlaying()) {
        m_playPauseButton->setText("Pause");
    } else {
        m_playPauseButton->setText("Play");
    }
}

QString MainWindow::formatTime(qint64 timeInMs)
{
    qint64 seconds = timeInMs / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;
    
    return QString("%1:%2")
        .arg(minutes, 2, 10, QChar('0'))
        .arg(seconds, 2, 10, QChar('0'));
}
