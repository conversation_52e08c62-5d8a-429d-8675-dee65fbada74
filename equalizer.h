#ifndef EQUALIZER_H
#define EQUALIZER_H

#include <QWidget>
#include <QSlider>
#include <QLabel>
#include <QComboBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QPushButton>

class Equalizer : public QWidget
{
    Q_OBJECT

public:
    struct EqualizerBand {
        QString name;
        double frequency;
        double gain;
        
        EqualizerBand(const QString &n, double freq, double g = 0.0)
            : name(n), frequency(freq), gain(g) {}
    };

    struct EqualizerPreset {
        QString name;
        QList<double> gains;
        
        EqualizerPreset(const QString &n, const QList<double> &g)
            : name(n), gains(g) {}
    };

    explicit Equalizer(QWidget *parent = nullptr);

    // Equalizer control
    void setEnabled(bool enabled);
    bool isEnabled() const { return m_enabled; }
    
    void setBandGain(int band, double gain);
    double getBandGain(int band) const;
    
    void applyPreset(const QString &presetName);
    void resetEqualizer();
    
    // Presets
    QStringList getPresetNames() const;
    void addCustomPreset(const QString &name, const QList<double> &gains);

signals:
    void equalizerStateChanged(bool enabled);
    void bandGainChanged(int band, double gain);
    void presetChanged(const QString &presetName);

private slots:
    void onBandSliderChanged(int value);
    void onPresetChanged(const QString &preset);
    void onEnabledToggled(bool enabled);
    void onResetClicked();

private:
    void setupUI();
    void setupPresets();
    void updateBandLabels();
    void applyPresetGains(const QList<double> &gains);

    static const int BAND_COUNT = 10;
    static const double MIN_GAIN;
    static const double MAX_GAIN;

    bool m_enabled;
    QList<EqualizerBand> m_bands;
    QList<EqualizerPreset> m_presets;
    
    // UI Components
    QCheckBox *m_enabledCheckBox;
    QComboBox *m_presetComboBox;
    QPushButton *m_resetButton;
    QList<QSlider*> m_bandSliders;
    QList<QLabel*> m_bandLabels;
    QList<QLabel*> m_frequencyLabels;
    QGroupBox *m_equalizerGroup;
};

#endif // EQUALIZER_H
